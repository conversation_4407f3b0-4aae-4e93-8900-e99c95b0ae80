-- =====================================================
-- SLACK SUMMARY SCRIBE - DEMO DATA SETUP
-- Production-Ready Public Demo Database Setup
-- =====================================================

-- First, create the demo user in auth.users (this would typically be done via Supabase Auth)
-- Note: In production, you'll need to create this user via Supabase Dashboard or Auth API

-- Demo user ID (consistent across all tables)
-- Use this UUID: 'demo-user-********-1234-1234-1234-********9012'
-- Demo org ID: 'demo-org-********-1234-1234-1234-********9012'

-- Create demo organization
INSERT INTO organizations (id, name, slug, settings, created_at, updated_at)
VALUES (
  'demo-org-********-1234-1234-1234-********9012',
  'Acme Corporation Demo',
  'acme-demo',
  '{"plan": "PRO", "features": ["ai_summaries", "exports", "notifications", "analytics"], "limits": {"summaries_per_month": 100, "workspaces": 3}}',
  NOW() - INTERVAL '30 days',
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  settings = EXCLUDED.settings,
  updated_at = NOW();

-- Create demo user profile (assumes auth.users record exists)
INSERT INTO users (id, name, email, avatar_url, provider, settings, last_active_at, created_at, updated_at)
VALUES (
  'demo-user-********-1234-1234-1234-********9012',
  'Alex Johnson',
  '<EMAIL>',
  'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  'demo',
  '{"theme": "light", "notifications": {"email": true, "slack": true}, "timezone": "America/New_York"}',
  NOW(),
  NOW() - INTERVAL '30 days',
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  email = EXCLUDED.email,
  avatar_url = EXCLUDED.avatar_url,
  settings = EXCLUDED.settings,
  last_active_at = NOW(),
  updated_at = NOW();

-- Create user-organization relationship
INSERT INTO user_organizations (id, user_id, organization_id, role, created_at)
VALUES (
  'demo-user-org-********-1234-1234-1234-********9012',
  'demo-user-********-1234-1234-1234-********9012',
  'demo-org-********-1234-1234-1234-********9012',
  'owner',
  NOW() - INTERVAL '30 days'
) ON CONFLICT (user_id, organization_id) DO UPDATE SET
  role = EXCLUDED.role;

-- Create Slack integrations for demo
INSERT INTO slack_integrations (id, organization_id, team_id, team_name, access_token, bot_user_id, scope, webhook_url, settings, is_active, created_at, updated_at)
VALUES 
  (
    'demo-slack-1-********-1234-1234-1234-********9012',
    'demo-org-********-1234-1234-1234-********9012',
    'T********90',
    'Acme Engineering',
    'demo-token-encrypted',
    'U********90',
    'chat:write,channels:read,groups:read,im:read,mpim:read',
    'https://hooks.slack.com/services/demo/webhook/url',
    '{"auto_summarize": true, "channels": ["general", "engineering", "product"], "summary_frequency": "daily"}',
    true,
    NOW() - INTERVAL '25 days',
    NOW()
  ),
  (
    'demo-slack-2-********-1234-1234-1234-********9012',
    'demo-org-********-1234-1234-1234-********9012',
    'T0987654321',
    'Acme Marketing',
    'demo-token-encrypted-2',
    'U0987654321',
    'chat:write,channels:read,groups:read',
    'https://hooks.slack.com/services/demo/webhook/url2',
    '{"auto_summarize": true, "channels": ["marketing", "campaigns"], "summary_frequency": "weekly"}',
    true,
    NOW() - INTERVAL '20 days',
    NOW()
  ),
  (
    'demo-slack-3-********-1234-1234-1234-********9012',
    'demo-org-********-1234-1234-1234-********9012',
    'T1122334455',
    'Acme Sales',
    'demo-token-encrypted-3',
    'U1122334455',
    'chat:write,channels:read',
    'https://hooks.slack.com/services/demo/webhook/url3',
    '{"auto_summarize": false, "channels": ["sales", "deals"], "summary_frequency": "manual"}',
    true,
    NOW() - INTERVAL '15 days',
    NOW()
  )
ON CONFLICT (id) DO UPDATE SET
  team_name = EXCLUDED.team_name,
  settings = EXCLUDED.settings,
  is_active = EXCLUDED.is_active,
  updated_at = NOW();

-- Create realistic demo summaries with varied content
INSERT INTO summaries (id, user_id, organization_id, title, content, summary_data, source, slack_channel, slack_message_ts, file_url, metadata, tags, created_at, updated_at)
VALUES
  (
    'demo-summary-1-********-1234-1234-1234-********9012',
    'demo-user-********-1234-1234-1234-********9012',
    'demo-org-********-1234-1234-1234-********9012',
    'Q4 Engineering Sprint Planning',
    'Team discussed upcoming Q4 sprint goals, resource allocation, and technical debt priorities. Key decisions made on microservices migration timeline and performance optimization targets.',
    '{"summary": "Engineering team aligned on Q4 priorities including microservices migration and performance improvements", "key_points": ["Microservices migration timeline set for Q4", "Performance optimization targets defined", "Resource allocation approved"], "action_items": ["Create migration roadmap", "Set up performance monitoring", "Schedule architecture review"], "participants": 8, "duration": "45 minutes", "sentiment": "positive"}',
    'slack',
    'engineering',
    '1703123456.789012',
    null,
    '{"ai_model": "deepseek-r1", "processing_time": 2340, "confidence": 0.92}',
    ARRAY['engineering', 'planning', 'q4', 'microservices'],
    NOW() - INTERVAL '2 days',
    NOW() - INTERVAL '2 days'
  ),
  (
    'demo-summary-2-********-1234-1234-1234-********9012',
    'demo-user-********-1234-1234-1234-********9012',
    'demo-org-********-1234-1234-1234-********9012',
    'Product Roadmap Review - Mobile App Features',
    'Product team reviewed mobile app feature requests, user feedback analysis, and competitive landscape. Prioritized push notifications, offline mode, and enhanced search functionality.',
    '{"summary": "Product roadmap updated with mobile-first features based on user feedback and competitive analysis", "key_points": ["Push notifications top priority", "Offline mode development approved", "Enhanced search functionality scoped"], "action_items": ["Create mobile feature specs", "Conduct user interviews", "Prototype offline functionality"], "participants": 6, "duration": "60 minutes", "sentiment": "enthusiastic"}',
    'slack',
    'product',
    '1703109876.543210',
    null,
    '{"ai_model": "gpt-4o-mini", "processing_time": 1890, "confidence": 0.89}',
    ARRAY['product', 'mobile', 'roadmap', 'features'],
    NOW() - INTERVAL '5 days',
    NOW() - INTERVAL '5 days'
  ),
  (
    'demo-summary-3-********-1234-1234-1234-********9012',
    'demo-user-********-1234-1234-1234-********9012',
    'demo-org-********-1234-1234-1234-********9012',
    'Marketing Campaign Performance Analysis',
    'Marketing team analyzed Q3 campaign performance across all channels. Social media campaigns showed 34% increase in engagement, email campaigns had 12% higher open rates, and paid ads achieved 2.3x ROAS.',
    '{"summary": "Q3 marketing campaigns exceeded targets with strong performance across all channels", "key_points": ["Social media engagement up 34%", "Email open rates improved 12%", "Paid ads achieved 2.3x ROAS"], "action_items": ["Scale successful social campaigns", "Optimize email templates", "Increase paid ad budget"], "participants": 5, "duration": "30 minutes", "sentiment": "celebratory"}',
    'upload',
    null,
    null,
    'https://demo-storage.supabase.co/marketing-analysis-q3.pdf',
    '{"ai_model": "deepseek-r1", "processing_time": 3120, "confidence": 0.94, "file_type": "pdf", "pages": 12}',
    ARRAY['marketing', 'analysis', 'q3', 'performance'],
    NOW() - INTERVAL '7 days',
    NOW() - INTERVAL '7 days'
  ),
  (
    'demo-summary-4-********-1234-1234-1234-********9012',
    'demo-user-********-1234-1234-1234-********9012',
    'demo-org-********-1234-1234-1234-********9012',
    'Customer Support Escalation Review',
    'Support team reviewed recent escalations and identified patterns in customer issues. Main concerns: API rate limiting, documentation clarity, and onboarding flow complexity.',
    '{"summary": "Support escalation analysis reveals opportunities for API improvements and better documentation", "key_points": ["API rate limiting causing issues", "Documentation needs clarity improvements", "Onboarding flow too complex"], "action_items": ["Review API rate limits", "Update documentation", "Simplify onboarding"], "participants": 4, "duration": "25 minutes", "sentiment": "constructive"}',
    'slack',
    'support',
    '1703087654.321098',
    null,
    '{"ai_model": "gpt-4o-mini", "processing_time": 1560, "confidence": 0.87}',
    ARRAY['support', 'escalation', 'api', 'documentation'],
    NOW() - INTERVAL '10 days',
    NOW() - INTERVAL '10 days'
  ),
  (
    'demo-summary-5-********-1234-1234-1234-********9012',
    'demo-user-********-1234-1234-1234-********9012',
    'demo-org-********-1234-1234-1234-********9012',
    'Sales Team Weekly Standup',
    'Sales team shared updates on pipeline progress, upcoming demos, and Q4 targets. Strong momentum with 3 enterprise deals in final stages and 15 new qualified leads this week.',
    '{"summary": "Sales team showing strong Q4 momentum with enterprise deals progressing and healthy lead generation", "key_points": ["3 enterprise deals in final stages", "15 new qualified leads", "Q4 targets on track"], "action_items": ["Prepare enterprise demo materials", "Follow up on qualified leads", "Update CRM pipeline"], "participants": 7, "duration": "20 minutes", "sentiment": "optimistic"}',
    'slack',
    'sales',
    '**********.109876',
    null,
    '{"ai_model": "deepseek-r1", "processing_time": 1234, "confidence": 0.91}',
    ARRAY['sales', 'standup', 'pipeline', 'enterprise'],
    NOW() - INTERVAL '12 days',
    NOW() - INTERVAL '12 days'
  ),
  (
    'demo-summary-6-********-1234-1234-1234-********9012',
    'demo-user-********-1234-1234-1234-********9012',
    'demo-org-********-1234-1234-1234-********9012',
    'Security Audit Findings Report',
    'Security team completed quarterly audit and identified several areas for improvement. No critical vulnerabilities found, but recommended updates to authentication flows and API security.',
    '{"summary": "Quarterly security audit completed with recommendations for authentication and API security improvements", "key_points": ["No critical vulnerabilities found", "Authentication flow updates needed", "API security enhancements recommended"], "action_items": ["Implement 2FA for admin accounts", "Update API rate limiting", "Schedule penetration testing"], "participants": 3, "duration": "40 minutes", "sentiment": "professional"}',
    'upload',
    null,
    null,
    'https://demo-storage.supabase.co/security-audit-q3.docx',
    '{"ai_model": "gpt-4o-mini", "processing_time": 2890, "confidence": 0.93, "file_type": "docx", "pages": 8}',
    ARRAY['security', 'audit', 'authentication', 'api'],
    NOW() - INTERVAL '15 days',
    NOW() - INTERVAL '15 days'
  )
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  content = EXCLUDED.content,
  summary_data = EXCLUDED.summary_data,
  metadata = EXCLUDED.metadata,
  tags = EXCLUDED.tags,
  updated_at = NOW();

-- Update organization settings with current usage stats
UPDATE organizations
SET settings = jsonb_set(
  settings,
  '{usage}',
  '{"summaries_this_month": 28, "total_summaries": 42, "workspaces_connected": 3, "last_summary": "2024-01-15T10:30:00Z"}'::jsonb
)
WHERE id = 'demo-org-********-1234-1234-1234-********9012';
