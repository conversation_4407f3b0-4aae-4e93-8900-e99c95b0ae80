/**
 * Dashboard API Route - Dev Mode (No Authentication)
 *
 * This route provides mock dashboard data for development:
 * - No authentication required
 * - Returns mock data for testing
 * - Development-grade performance
 * - Mock summaries, workspaces, and user stats
 *
 * Expected response time: <100ms
 */
import { NextRequest, NextResponse } from 'next/server';
import { DEFAULT_DEV_USER } from '@/lib/dev-auth';

export async function GET(request: NextRequest) {
  const requestStartTime = Date.now();
  const requestId = request.headers.get('X-Request-ID') || `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  console.log(`📊 [${requestId}] Dashboard API called - Dev Mode (No Auth)`);

  // Use default dev user (no authentication required)
  const user = DEFAULT_DEV_USER;
  const userId = user.id;

  try {
    // Performance monitoring
    const startTime = Date.now();

    // Add cache headers for better performance
    const cacheHeaders = {
      'Cache-Control': 'private, max-age=30, s-maxage=60', // Private cache for user data
      'Content-Type': 'application/json',
    };

    console.log(`📊 Dashboard: Generating mock data for dev user ${userId}...`);

    // Generate mock data instead of fetching from Supabase
    const summariesData = [
      {
        id: 'mock-summary-1',
        title: 'Weekly Team Standup',
        content: 'Team discussed project progress, upcoming deadlines, and resource allocation.',
        created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        user_id: userId,
        file_name: 'standup-notes.pdf',
        file_size: 245760,
        summary_length: 150,
        processing_time: 2.3,
        ai_model: 'deepseek-r1'
      },
      {
        id: 'mock-summary-2',
        title: 'Product Roadmap Review',
        content: 'Reviewed Q1 roadmap, prioritized features, and discussed technical requirements.',
        created_at: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
        user_id: userId,
        file_name: 'roadmap-meeting.docx',
        file_size: 156432,
        summary_length: 220,
        processing_time: 3.1,
        ai_model: 'deepseek-r1'
      },
      {
        id: 'mock-summary-3',
        title: 'Client Feedback Session',
        content: 'Gathered valuable feedback on new features, identified improvement areas.',
        created_at: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
        user_id: userId,
        file_name: 'client-feedback.pdf',
        file_size: 189234,
        summary_length: 180,
        processing_time: 2.8,
        ai_model: 'deepseek-r1'
      }
    ];

    // Transform mock summaries to match frontend Summary interface
    const recentSummaries = summariesData.map(summary => ({
      id: summary.id,
      title: summary.title || 'Untitled Summary',
      content: summary.content || '',
      keyPoints: [
        'Key discussion points identified',
        'Action items assigned to team members',
        'Next steps clearly defined'
      ],
      actionItems: [
        'Follow up on pending tasks',
        'Schedule next review meeting',
        'Update project documentation'
      ],
      participants: ['Alice Johnson', 'Bob Smith', 'Carol Davis'],
      duration: 45, // Mock duration in minutes
      createdAt: summary.created_at,
      updatedAt: summary.created_at,
      status: 'completed' as const,
      source: 'upload' as const,
      workspaceId: user.orgId,
      workspaceName: user.orgName,
      channelName: '#general',
      userId: summary.user_id,
      tags: ['meeting', 'team', 'weekly']
    }));

    // Generate mock workspace data
    const workspacesData = [
      {
        id: user.orgId,
        name: user.orgName,
        connected: true,
        slack_team_id: 'T1234567890',
        created_at: new Date(Date.now() - 604800000).toISOString(), // 1 week ago
        user_id: userId
      }
    ];

    // Calculate stats from mock data
    const totalSummaries = summariesData.length;
    const workspacesConnected = workspacesData.length;
    const thisMonth = new Date();
    thisMonth.setDate(1); // First day of current month
    const summariesThisMonth = summariesData.filter(
      summary => new Date(summary.created_at) >= thisMonth
    ).length;

    // Transform workspace data to match frontend interface
    const slackWorkspaces = workspacesData.map(workspace => ({
      id: workspace.id,
      name: workspace.name || 'Unknown Workspace',
      connected: workspace.connected !== false,
      team_id: workspace.slack_team_id
    }));

    // Use dev user data (no database required)
    const userProfile = {
      full_name: user.name,
      email: user.email,
      avatar_url: user.avatar,
      subscription_plan: user.plan,
      subscription_status: 'active'
    };

    console.log(`📊 [${requestId}] Using dev user profile: ${user.name}`);

    // Mock data matching DashboardData interface
    const dashboardData = {
      user: {
        id: userId,
        name: userProfile.full_name,
        email: userProfile.email,
        avatar_url: userProfile.avatar_url
      },
      subscription: {
        plan: userProfile.subscription_plan,
        status: userProfile.subscription_status
      },
      stats: {
        totalSummaries,
        workspacesConnected,
        summariesThisMonth
      },
      slackWorkspaces,
      recentSummaries,
      notifications: [
        {
          id: 'notif-1',
          type: 'info',
          title: 'Welcome to Dev Mode!',
          message: 'You are using the development version with mock data. All features are available for testing.',
          timestamp: new Date().toISOString(),
          read: false
        },
        {
          id: 'notif-2',
          type: 'success',
          title: 'Slack Workspace Connected',
          message: `Successfully connected to ${user.orgName} workspace.`,
          timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
          read: false
        }
      ]
    };

    const endTime = Date.now();
    const totalTime = endTime - requestStartTime;

    console.log(`📊 [${requestId}] Dashboard data fetched successfully in ${totalTime}ms`);
    console.log(`📊 [${requestId}] Found ${totalSummaries} summaries, ${workspacesConnected} workspaces`);

    // Return live data with proper headers
    return NextResponse.json({
      success: true,
      data: dashboardData,
      requestId,
      timing: {
        total: `${totalTime}ms`,
        dataFetch: `${endTime - startTime}ms`
      },
      timestamp: new Date().toISOString()
    }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=30', // Cache for 30 seconds (shorter for live data)
        'X-Request-ID': requestId,
      }
    });

  } catch (error: any) {
    const errorTime = Date.now() - requestStartTime;
    console.error(`❌ [${requestId}] Dashboard API error after ${errorTime}ms:`, error);

    // Return fallback data on error to prevent dashboard crashes
    return NextResponse.json({
      success: false,
      error: 'Failed to load dashboard data',
      message: error.message,
      data: {
        user: {
          id: userId || 'unknown',
          name: 'User',
          email: '<EMAIL>',
          avatar_url: null
        },
        subscription: {
          plan: 'Free',
          status: 'active'
        },
        stats: {
          totalSummaries: 0,
          workspacesConnected: 0,
          summariesThisMonth: 0
        },
        slackWorkspaces: [],
        recentSummaries: [],
        notifications: []
      },
      requestId,
      timestamp: new Date().toISOString()
    }, {
      status: 200, // Return 200 with fallback data instead of 500
      headers: {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId,
      }
    });
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { 
      error: 'Method not allowed. Use GET to fetch dashboard data.' 
    },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { 
      error: 'Method not allowed. Use GET to fetch dashboard data.' 
    },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { 
      error: 'Method not allowed. Use GET to fetch dashboard data.' 
    },
    { status: 405 }
  );
}
