#!/usr/bin/env node

/**
 * Fix Demo Imports Script
 * 
 * Automatically replaces all demo-constants imports with real authentication
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Files to process
const patterns = [
  'app/api/**/*.ts',
  'components/**/*.tsx',
  'lib/**/*.ts'
];

// Replacement mappings
const replacements = [
  {
    from: "import { DEMO_USER_ID } from '@/lib/demo-constants';",
    to: "import { getCurrentUser } from '@/lib/user-management';"
  },
  {
    from: "import { DEMO_USER_ID, DEMO_ORG_ID } from '@/lib/demo-constants';",
    to: "import { getCurrentUser } from '@/lib/user-management';\nimport { createSupabaseServerClient } from '@/lib/supabase-server';"
  },
  {
    from: "import { DEMO_USER } from '@/lib/demo-constants';",
    to: "import { getCurrentUserClient } from '@/lib/user-management';"
  },
  {
    from: /import.*from '@\/lib\/demo-constants';?/g,
    to: "import { getCurrentUser } from '@/lib/user-management';\nimport { createSupabaseServerClient } from '@/lib/supabase-server';"
  },
  {
    from: /const userId = DEMO_USER_ID;?/g,
    to: "const user = await getCurrentUser();\n  if (!user) {\n    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });\n  }\n  const userId = user.id;"
  },
  {
    from: /DEMO_USER_ID/g,
    to: "user.id"
  },
  {
    from: /DEMO_ORG_ID/g,
    to: "user.organization_id"
  }
];

function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Skip if file doesn't contain demo imports
    if (!content.includes('demo-constants')) {
      return false;
    }

    console.log(`Processing: ${filePath}`);

    // Apply replacements
    replacements.forEach(replacement => {
      const originalContent = content;
      content = content.replace(replacement.from, replacement.to);
      if (content !== originalContent) {
        modified = true;
      }
    });

    // Write back if modified
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`  ✅ Updated: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`  ❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function fixDemoImports() {
  console.log('🔧 Fixing demo-constants imports...\n');

  let totalFiles = 0;
  let modifiedFiles = 0;

  patterns.forEach(pattern => {
    const files = glob.sync(pattern, { ignore: ['node_modules/**', '.next/**'] });
    
    files.forEach(file => {
      totalFiles++;
      if (processFile(file)) {
        modifiedFiles++;
      }
    });
  });

  console.log(`\n📊 Summary:`);
  console.log(`  Total files processed: ${totalFiles}`);
  console.log(`  Files modified: ${modifiedFiles}`);
  
  if (modifiedFiles > 0) {
    console.log(`\n✅ Demo imports fixed! Run 'npm run build' to verify.`);
  } else {
    console.log(`\n✅ No demo imports found.`);
  }
}

// Run the fix
if (require.main === module) {
  fixDemoImports();
}

module.exports = { fixDemoImports };
