import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Slack status check');

    // Demo user ID for public mode
    const userId = 'demo-user';

    // Create Supabase client
    const supabase = await createSupabaseServerClient();

    // Check for existing Slack integration
    const { data: integration, error: integrationError } = await supabase
      .from('slack_integrations')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .single();

    if (integrationError && integrationError.code !== 'PGRST116') {
      console.error('Error checking Slack integration:', integrationError);
      return NextResponse.json(
        { success: false, error: 'Failed to check integration status' },
        { status: 500 }
      );
    }

    const connected = !!integration;

    console.log('🔍 Slack status for user:', userId, 'Connected:', connected);

    return NextResponse.json({
      success: true,
      data: {
        connected,
        integration: connected ? {
          team_id: integration.team_id,
          team_name: integration.team_name,
          connected_at: integration.created_at,
          scope: integration.scope
        } : null
      }
    });

  } catch (error) {
    console.error('❌ Slack status error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
