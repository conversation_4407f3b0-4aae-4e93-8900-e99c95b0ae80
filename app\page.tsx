'use client';

export const dynamic = 'force-dynamic';

import Link from 'next/link';
import { ArrowRight, MessageSquare, FileText, BarChart3, Zap } from 'lucide-react';
import ChunkErrorBoundary from '@/components/ChunkErrorBoundary';

// Simplified Logo component to avoid potential issues
function SimpleLogo() {
  return (
    <div className="flex items-center space-x-2">
      <MessageSquare className="h-8 w-8 text-blue-600" />
      <span className="text-xl font-bold text-gray-900">
        Slack Summary Scribe
      </span>
    </div>
  );
}

function HomePageContent() {
  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Public Header - No Authentication Required */}
      <header className="w-full bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-6">
          <nav className="flex items-center justify-between">
            <SimpleLogo />
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700 transition-colors">
                Dashboard
              </Link>
              <Link href="/upload" className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                Start Now
              </Link>
            </div>
          </nav>
        </div>
      </header>

      {/* Public Hero Section */}
      <section className="w-full bg-gray-50">
        <div className="container mx-auto px-4 py-20 text-center">
          <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6">
            <Zap className="h-4 w-4 mr-2" />
            🚀 v1.0.0 Public Live - No Signup Required
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6" role="banner">
            AI-Powered Slack Summaries
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto" role="doc-subtitle">
            Transform your Slack conversations and documents into actionable insights with AI-powered summaries. Try it now - no registration needed!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center" role="group" aria-label="Main navigation actions">
            <Link
              href="/dashboard"
              className="inline-flex items-center px-8 py-3 text-lg bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              aria-label="Open dashboard to start using Slack Summary Scribe"
            >
              Try Live Demo
              <ArrowRight className="ml-2 h-5 w-5" aria-hidden="true" />
            </Link>
            <Link
              href="/upload"
              className="inline-flex items-center px-8 py-3 text-lg border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700 transition-colors focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              aria-label="Upload documents for AI summarization"
            >
              Upload Document
              <FileText className="ml-2 h-5 w-5" aria-hidden="true" />
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="w-full py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Experience All Features in Demo Mode
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Explore the full functionality of our AI-powered summarization platform without any signup required.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <MessageSquare className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Slack Integration</h3>
              <p className="text-gray-600">
                Connect your Slack workspaces and automatically generate summaries from conversations.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <FileText className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Document Upload</h3>
              <p className="text-gray-600">
                Upload PDF and DOCX files to get instant AI-powered summaries and insights.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Analytics Dashboard</h3>
              <p className="text-gray-600">
                View comprehensive analytics and insights about your summaries and usage patterns.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Simple Footer */}
      <footer className="mt-auto border-t bg-white">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <SimpleLogo />
            <div className="mt-4 text-sm text-gray-500">
              © 2024 Slack Summary Scribe. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default function HomePage() {
  return (
    <ChunkErrorBoundary>
      <HomePageContent />
    </ChunkErrorBoundary>
  );
}
