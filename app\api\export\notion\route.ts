import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('📝 Notion Export API: Auth disabled - returning demo export');

    const { summaryId } = await request.json();

    if (!summaryId) {
      return NextResponse.json(
        { success: false, error: 'Summary ID is required' },
        { status: 400 }
      );
    }

    // Get summary data (demo mode)
    const summary = {
      id: summaryId,
      title: 'Demo Slack Summary',
      summary_text: 'This is a comprehensive demo summary of team discussions covering project updates, technical decisions, and action items. The team discussed the upcoming product launch, reviewed technical architecture decisions, and assigned responsibilities for the next sprint.',
      channel_name: '#general',
      message_count: 25,
      created_at: new Date().toISOString(),
      skills_detected: ['Project Management', 'Technical Architecture', 'Team Coordination'],
      red_flags: ['Potential deadline risk', 'Resource allocation concern'],
      actions: ['Schedule follow-up meeting', 'Update project timeline', 'Review resource allocation'],
      tags: ['urgent', 'product-launch', 'architecture'],
      file_uploads: [{
        file_name: 'demo-document.pdf',
        file_size: 1024000,
        file_type: 'application/pdf',
        created_at: new Date().toISOString()
      }]
    };

    // Convert summary to Notion-compatible markdown format
    const notionContent = generateNotionMarkdown(summary);

    // Create a downloadable markdown file
    const buffer = Buffer.from(notionContent, 'utf-8');

    // Log export activity (demo mode - no actual logging)
    console.log('📝 Notion Export: Demo export completed successfully');

    // Create notification (demo mode - no actual notification)
    console.log('🔔 Notion Export: Demo notification created');

    // Return markdown file
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'text/markdown',
        'Content-Disposition': `attachment; filename="${summary.title || 'summary'}.md"`,
        'Content-Length': buffer.length.toString()
      }
    });

  } catch (error) {
    console.error('Notion export error:', error);
    
    // Log failed export (demo mode - no actual logging)
    console.log('❌ Notion Export: Demo export failed');

    return NextResponse.json(
      { success: false, error: 'Export failed' },
      { status: 500 }
    );
  }
}

function generateNotionMarkdown(summary: any): string {
  const title = summary.title || 'Untitled Summary';
  const content = summary.content || 'No content available';
  const createdAt = new Date(summary.created_at).toLocaleString();
  const sourceType = summary.source_type || 'Unknown';
  const fileName = summary.file_name || 'N/A';

  let markdown = `# ${title}\n\n`;

  // Add metadata section
  markdown += `## Document Information\n\n`;
  markdown += `- **Created:** ${createdAt}\n`;
  markdown += `- **Source Type:** ${sourceType}\n`;
  markdown += `- **File Name:** ${fileName}\n`;

  if (summary.file_uploads) {
    const fileInfo = summary.file_uploads;
    markdown += `- **File Size:** ${(fileInfo.file_size / 1024 / 1024).toFixed(2)} MB\n`;
    markdown += `- **File Type:** ${fileInfo.file_type}\n`;
    markdown += `- **Upload Date:** ${new Date(fileInfo.created_at).toLocaleString()}\n`;
  }

  markdown += `\n---\n\n`;

  // Add summary content
  markdown += `## Summary\n\n`;
  
  // Process content to ensure proper markdown formatting
  const processedContent = content
    .split('\n')
    .map((line: string) => {
      const trimmed = line.trim();
      if (!trimmed) return '';
      
      // Convert bullet points
      if (trimmed.startsWith('•') || trimmed.startsWith('-')) {
        return `- ${trimmed.substring(1).trim()}`;
      }
      
      // Convert numbered lists
      if (/^\d+\./.test(trimmed)) {
        return trimmed;
      }
      
      // Convert headers (if they exist)
      if (trimmed.startsWith('#')) {
        return `### ${trimmed.substring(1).trim()}`;
      }
      
      return trimmed;
    })
    .join('\n');

  markdown += processedContent;

  // Add metadata section if available
  if (summary.metadata) {
    markdown += `\n\n---\n\n## Technical Metadata\n\n`;
    
    const metadata = summary.metadata as any;
    Object.entries(metadata).forEach(([key, value]) => {
      markdown += `- **${key.charAt(0).toUpperCase() + key.slice(1)}:** ${String(value)}\n`;
    });
  }

  // Add footer
  markdown += `\n\n---\n\n`;
  markdown += `*Generated by Slack Summary Scribe on ${new Date().toLocaleString()}*\n`;
  markdown += `*Import this file into Notion by copying and pasting the content or using Notion's import feature.*\n`;

  return markdown;
}
