/**
 * Startup Health Check Utility
 * 
 * Performs essential health checks during application startup
 * to ensure all critical services are available and properly configured.
 */

import { createClient } from '@supabase/supabase-js';

interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  message: string;
  responseTime?: number;
  error?: string;
}

interface StartupHealthReport {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  checks: HealthCheckResult[];
  criticalFailures: string[];
  warnings: string[];
}

/**
 * Check Supabase database connectivity
 */
async function checkSupabaseHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      return {
        service: 'Supabase Database',
        status: 'unhealthy',
        message: 'Supabase configuration missing',
        error: 'NEXT_PUBLIC_SUPABASE_URL or NEXT_PUBLIC_SUPABASE_ANON_KEY not set'
      };
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    );

    // Test basic connectivity with a simple query
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);

    const responseTime = Date.now() - startTime;

    if (error && !error.message.includes('No rows')) {
      return {
        service: 'Supabase Database',
        status: 'unhealthy',
        message: 'Database connection failed',
        responseTime,
        error: error.message
      };
    }

    if (responseTime > 5000) {
      return {
        service: 'Supabase Database',
        status: 'degraded',
        message: 'Database responding slowly',
        responseTime
      };
    }

    return {
      service: 'Supabase Database',
      status: 'healthy',
      message: 'Database connection successful',
      responseTime
    };

  } catch (error) {
    return {
      service: 'Supabase Database',
      status: 'unhealthy',
      message: 'Database connection failed',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Check OpenRouter AI service availability
 */
async function checkOpenRouterHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now();

  try {
    if (!process.env.OPENROUTER_API_KEY) {
      return {
        service: 'OpenRouter AI',
        status: 'unhealthy',
        message: 'OpenRouter API key not configured',
        error: 'OPENROUTER_API_KEY not set'
      };
    }

    const response = await fetch('https://openrouter.ai/api/v1/models', {
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
        'X-Title': 'Slack Summary Scribe'
      },
      signal: AbortSignal.timeout(10000) // 10 second timeout
    });

    const responseTime = Date.now() - startTime;

    if (!response.ok) {
      return {
        service: 'OpenRouter AI',
        status: 'unhealthy',
        message: 'AI service unavailable',
        responseTime,
        error: `HTTP ${response.status}: ${response.statusText}`
      };
    }

    if (responseTime > 5000) {
      return {
        service: 'OpenRouter AI',
        status: 'degraded',
        message: 'AI service responding slowly',
        responseTime
      };
    }

    return {
      service: 'OpenRouter AI',
      status: 'healthy',
      message: 'AI service available',
      responseTime
    };

  } catch (error) {
    return {
      service: 'OpenRouter AI',
      status: 'unhealthy',
      message: 'AI service connection failed',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Check environment configuration
 */
function checkEnvironmentConfig(): HealthCheckResult {
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'JWT_SECRET',
    'NEXTAUTH_SECRET',
    'SLACK_CLIENT_ID',
    'SLACK_CLIENT_SECRET',
    'OPENROUTER_API_KEY'
  ];

  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    return {
      service: 'Environment Config',
      status: 'unhealthy',
      message: 'Critical environment variables missing',
      error: `Missing: ${missing.join(', ')}`
    };
  }

  // Check URL consistency
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;
  const nextAuthUrl = process.env.NEXTAUTH_URL;
  
  if (siteUrl && nextAuthUrl && siteUrl !== nextAuthUrl) {
    return {
      service: 'Environment Config',
      status: 'degraded',
      message: 'URL configuration inconsistency detected',
      error: 'NEXT_PUBLIC_SITE_URL and NEXTAUTH_URL should match'
    };
  }

  return {
    service: 'Environment Config',
    status: 'healthy',
    message: 'All required environment variables configured'
  };
}

/**
 * Perform comprehensive startup health check
 */
export async function performStartupHealthCheck(): Promise<StartupHealthReport> {
  console.log('🏥 Performing startup health check...');
  
  const startTime = Date.now();
  const checks: HealthCheckResult[] = [];

  // Run all health checks
  checks.push(checkEnvironmentConfig());
  checks.push(await checkSupabaseHealth());
  checks.push(await checkOpenRouterHealth());

  // Analyze results
  const criticalFailures: string[] = [];
  const warnings: string[] = [];
  
  for (const check of checks) {
    if (check.status === 'unhealthy') {
      criticalFailures.push(`${check.service}: ${check.message}`);
    } else if (check.status === 'degraded') {
      warnings.push(`${check.service}: ${check.message}`);
    }
  }

  // Determine overall status
  let overall: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
  if (criticalFailures.length > 0) {
    overall = 'unhealthy';
  } else if (warnings.length > 0) {
    overall = 'degraded';
  }

  const totalTime = Date.now() - startTime;
  
  const report: StartupHealthReport = {
    overall,
    timestamp: new Date().toISOString(),
    checks,
    criticalFailures,
    warnings
  };

  // Log results
  console.log(`🏥 Health check completed in ${totalTime}ms - Overall status: ${overall}`);
  
  for (const check of checks) {
    const icon = check.status === 'healthy' ? '✅' : check.status === 'degraded' ? '⚠️' : '❌';
    const timeStr = check.responseTime ? ` (${check.responseTime}ms)` : '';
    console.log(`  ${icon} ${check.service}: ${check.message}${timeStr}`);
    if (check.error) {
      console.log(`    Error: ${check.error}`);
    }
  }

  if (criticalFailures.length > 0) {
    console.warn('🚨 Critical failures detected:');
    criticalFailures.forEach(failure => console.warn(`  - ${failure}`));
  }

  if (warnings.length > 0) {
    console.warn('⚠️ Warnings:');
    warnings.forEach(warning => console.warn(`  - ${warning}`));
  }

  return report;
}

/**
 * Graceful degradation helper
 */
export function shouldEnableFeature(featureName: string, healthReport?: StartupHealthReport): boolean {
  if (!healthReport) return true;

  switch (featureName) {
    case 'ai-summarization':
      return !healthReport.criticalFailures.some(f => f.includes('OpenRouter'));
    
    case 'database-features':
      return !healthReport.criticalFailures.some(f => f.includes('Supabase'));
    
    case 'slack-integration':
      return !!(process.env.SLACK_CLIENT_ID && process.env.SLACK_CLIENT_SECRET);
    
    default:
      return true;
  }
}

/**
 * Get health status for monitoring
 */
export function getHealthStatus(): 'healthy' | 'degraded' | 'unhealthy' {
  // This would typically check cached health status
  // For now, return healthy if basic env vars are present
  const hasBasicConfig = !!(
    process.env.NEXT_PUBLIC_SUPABASE_URL &&
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&
    process.env.OPENROUTER_API_KEY
  );
  
  return hasBasicConfig ? 'healthy' : 'unhealthy';
}
