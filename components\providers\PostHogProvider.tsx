'use client';

import React, { useEffect, useState } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';

interface PostHogProviderProps {
  children: React.ReactNode;
}

export default function PostHogProvider({ children }: PostHogProviderProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Safely initialize PostHog with error handling
    const initializeAnalytics = async () => {
      try {
        if (typeof window !== 'undefined') {
          const { initializePostHog } = await import('@/lib/analytics');
          await initializePostHog();
          setIsInitialized(true);
        }
      } catch (error) {
        console.warn('PostHog initialization failed:', error);
        // Continue without analytics rather than breaking the app
        setIsInitialized(true);
      }
    };

    initializeAnalytics();
  }, []);

  useEffect(() => {
    // Track page views only after initialization
    if (isInitialized && pathname) {
      const trackPageViewSafely = async () => {
        try {
          const { trackPageView } = await import('@/lib/analytics');
          const url = pathname + (searchParams?.toString() ? `?${searchParams.toString()}` : '');
          trackPageView(url);
        } catch (error) {
          console.warn('Page view tracking failed:', error);
          // Continue silently without breaking the app
        }
      };

      trackPageViewSafely();
    }
  }, [pathname, searchParams, isInitialized]);

  return <>{children}</>;
}
