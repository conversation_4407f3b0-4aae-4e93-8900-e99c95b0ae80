'use client';

/**
 * Comprehensive Export System
 * 
 * Features:
 * ✅ PDF export with styling
 * ✅ Excel export with structured data
 * ✅ Notion export with markdown
 * ✅ Export tracking and logs
 * ✅ Email and Slack delivery
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Download, 
  FileText, 
  Table, 
  BookOpen, 
  Mail, 
  MessageSquare,
  Loader2,
  CheckCircle,
  AlertCircle,
  Share,
  Settings
} from 'lucide-react';
import { toast } from 'sonner';
import { analytics } from '@/lib/posthog.client';

interface ExportOptions {
  format: 'pdf' | 'excel' | 'notion';
  includeMetadata: boolean;
  includeCharts: boolean;
  customTemplate: boolean;
  emailDelivery: boolean;
  slackDelivery: boolean;
  recipientEmail?: string;
  slackChannel?: string;
  notes?: string;
}

interface ExportJob {
  id: string;
  summaryId: string;
  format: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  downloadUrl?: string;
  error?: string;
  createdAt: Date;
  completedAt?: Date;
}

interface ExportSystemProps {
  summaryId: string;
  summaryTitle: string;
  onExportComplete?: (job: ExportJob) => void;
}

export default function ExportSystem({ 
  summaryId, 
  summaryTitle, 
  onExportComplete 
}: ExportSystemProps) {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    includeMetadata: true,
    includeCharts: false,
    customTemplate: false,
    emailDelivery: false,
    slackDelivery: false
  });

  const [exportJobs, setExportJobs] = useState<ExportJob[]>([]);
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    if (!summaryId) {
      toast.error('No summary selected for export');
      return;
    }

    setIsExporting(true);

    try {
      // Create export job
      const jobId = `export_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const newJob: ExportJob = {
        id: jobId,
        summaryId,
        format: exportOptions.format,
        status: 'pending',
        progress: 0,
        createdAt: new Date()
      };

      setExportJobs(prev => [newJob, ...prev]);

      // Track export start
      analytics.track('export_started', {
        export_id: jobId,
        summary_id: summaryId,
        format: exportOptions.format,
        include_metadata: exportOptions.includeMetadata,
        include_charts: exportOptions.includeCharts,
        email_delivery: exportOptions.emailDelivery,
        slack_delivery: exportOptions.slackDelivery
      });

      // Update job status
      updateJobStatus(jobId, 'processing', 10);

      // Call appropriate export API
      let response;
      switch (exportOptions.format) {
        case 'pdf':
          response = await exportToPDF(summaryId, exportOptions);
          break;
        case 'excel':
          response = await exportToExcel(summaryId, exportOptions);
          break;
        case 'notion':
          response = await exportToNotion(summaryId, exportOptions);
          break;
        default:
          throw new Error('Unsupported export format');
      }

      updateJobStatus(jobId, 'processing', 70);

      // Handle delivery options
      if (exportOptions.emailDelivery && exportOptions.recipientEmail) {
        await sendEmailDelivery(response.downloadUrl, exportOptions.recipientEmail, summaryTitle);
        updateJobStatus(jobId, 'processing', 85);
      }

      if (exportOptions.slackDelivery && exportOptions.slackChannel) {
        await sendSlackDelivery(response.downloadUrl, exportOptions.slackChannel, summaryTitle);
        updateJobStatus(jobId, 'processing', 95);
      }

      // Complete the job
      updateJobStatus(jobId, 'completed', 100, response.downloadUrl);

      // Track completion
      analytics.track('export_completed', {
        export_id: jobId,
        summary_id: summaryId,
        format: exportOptions.format,
        file_size: response.fileSize,
        processing_time: Date.now() - newJob.createdAt.getTime()
      });

      toast.success(`${exportOptions.format.toUpperCase()} export completed successfully!`);
      onExportComplete?.(newJob);

    } catch (error) {
      console.error('Export error:', error);
      
      // Update job with error
      const failedJob = exportJobs.find(job => job.summaryId === summaryId && job.status === 'processing');
      if (failedJob) {
        updateJobStatus(failedJob.id, 'failed', 0, undefined, error instanceof Error ? error.message : 'Export failed');
      }

      analytics.trackError('export_failed', {
        summary_id: summaryId,
        format: exportOptions.format,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      toast.error(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsExporting(false);
    }
  };

  const updateJobStatus = (
    jobId: string, 
    status: ExportJob['status'], 
    progress: number, 
    downloadUrl?: string, 
    error?: string
  ) => {
    setExportJobs(prev => prev.map(job => 
      job.id === jobId 
        ? { 
            ...job, 
            status, 
            progress, 
            downloadUrl,
            error,
            completedAt: status === 'completed' || status === 'failed' ? new Date() : undefined
          }
        : job
    ));
  };

  const exportToPDF = async (summaryId: string, options: ExportOptions) => {
    const response = await fetch('/api/export/pdf', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        summaryId,
        includeMetadata: options.includeMetadata,
        includeCharts: options.includeCharts,
        customTemplate: options.customTemplate,
        notes: options.notes
      })
    });

    if (!response.ok) {
      throw new Error(`PDF export failed: ${response.statusText}`);
    }

    const blob = await response.blob();
    const downloadUrl = URL.createObjectURL(blob);
    
    return {
      downloadUrl,
      fileSize: blob.size,
      mimeType: 'application/pdf'
    };
  };

  const exportToExcel = async (summaryId: string, options: ExportOptions) => {
    const response = await fetch('/api/export/excel', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        summaryId,
        includeMetadata: options.includeMetadata,
        includeCharts: options.includeCharts,
        notes: options.notes
      })
    });

    if (!response.ok) {
      throw new Error(`Excel export failed: ${response.statusText}`);
    }

    const blob = await response.blob();
    const downloadUrl = URL.createObjectURL(blob);
    
    return {
      downloadUrl,
      fileSize: blob.size,
      mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    };
  };

  const exportToNotion = async (summaryId: string, options: ExportOptions) => {
    const response = await fetch('/api/export/notion', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        summaryId,
        includeMetadata: options.includeMetadata,
        notes: options.notes
      })
    });

    if (!response.ok) {
      throw new Error(`Notion export failed: ${response.statusText}`);
    }

    const data = await response.json();
    return {
      downloadUrl: data.downloadUrl,
      fileSize: data.fileSize,
      notionPageUrl: data.notionPageUrl
    };
  };

  const sendEmailDelivery = async (downloadUrl: string, email: string, title: string) => {
    await fetch('/api/export/email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        downloadUrl,
        recipientEmail: email,
        summaryTitle: title,
        format: exportOptions.format
      })
    });
  };

  const sendSlackDelivery = async (downloadUrl: string, channel: string, title: string) => {
    await fetch('/api/export/slack', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        downloadUrl,
        slackChannel: channel,
        summaryTitle: title,
        format: exportOptions.format
      })
    });
  };

  const downloadFile = (job: ExportJob) => {
    if (job.downloadUrl) {
      const link = document.createElement('a');
      link.href = job.downloadUrl;
      link.download = `${summaryTitle}_${job.format}_${job.id}.${getFileExtension(job.format)}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      analytics.track('export_downloaded', {
        export_id: job.id,
        summary_id: summaryId,
        format: job.format
      });
    }
  };

  const getFileExtension = (format: string) => {
    switch (format) {
      case 'pdf': return 'pdf';
      case 'excel': return 'xlsx';
      case 'notion': return 'md';
      default: return 'txt';
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf': return <FileText className="h-5 w-5 text-red-500" />;
      case 'excel': return <Table className="h-5 w-5 text-green-500" />;
      case 'notion': return <BookOpen className="h-5 w-5 text-purple-500" />;
      default: return <Download className="h-5 w-5" />;
    }
  };

  const getStatusIcon = (status: ExportJob['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed': return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'processing': case 'pending': return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Export Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Summary
          </CardTitle>
          <CardDescription>
            Export "{summaryTitle}" in your preferred format
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Format Selection */}
          <div>
            <Label className="text-base font-medium">Export Format</Label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mt-2">
              {[
                { value: 'pdf', label: 'PDF Document', icon: FileText, description: 'Styled PDF with formatting' },
                { value: 'excel', label: 'Excel Spreadsheet', icon: Table, description: 'Structured data in XLSX' },
                { value: 'notion', label: 'Notion Page', icon: BookOpen, description: 'Markdown for Notion' }
              ].map((format) => (
                <div
                  key={format.value}
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    exportOptions.format === format.value 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setExportOptions(prev => ({ ...prev, format: format.value as any }))}
                >
                  <div className="flex items-center gap-3">
                    <format.icon className="h-5 w-5" />
                    <div>
                      <p className="font-medium">{format.label}</p>
                      <p className="text-sm text-gray-500">{format.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Export Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <Label className="text-base font-medium">Content Options</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="metadata"
                    checked={exportOptions.includeMetadata}
                    onCheckedChange={(checked) => 
                      setExportOptions(prev => ({ ...prev, includeMetadata: !!checked }))
                    }
                  />
                  <Label htmlFor="metadata">Include metadata & timestamps</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="charts"
                    checked={exportOptions.includeCharts}
                    onCheckedChange={(checked) => 
                      setExportOptions(prev => ({ ...prev, includeCharts: !!checked }))
                    }
                  />
                  <Label htmlFor="charts">Include charts & visualizations</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="template"
                    checked={exportOptions.customTemplate}
                    onCheckedChange={(checked) => 
                      setExportOptions(prev => ({ ...prev, customTemplate: !!checked }))
                    }
                  />
                  <Label htmlFor="template">Use custom template</Label>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <Label className="text-base font-medium">Delivery Options</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="email"
                    checked={exportOptions.emailDelivery}
                    onCheckedChange={(checked) => 
                      setExportOptions(prev => ({ ...prev, emailDelivery: !!checked }))
                    }
                  />
                  <Label htmlFor="email">Send via email</Label>
                </div>
                {exportOptions.emailDelivery && (
                  <Input
                    placeholder="<EMAIL>"
                    value={exportOptions.recipientEmail || ''}
                    onChange={(e) => 
                      setExportOptions(prev => ({ ...prev, recipientEmail: e.target.value }))
                    }
                  />
                )}
                
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="slack"
                    checked={exportOptions.slackDelivery}
                    onCheckedChange={(checked) => 
                      setExportOptions(prev => ({ ...prev, slackDelivery: !!checked }))
                    }
                  />
                  <Label htmlFor="slack">Post to Slack</Label>
                </div>
                {exportOptions.slackDelivery && (
                  <Input
                    placeholder="#general"
                    value={exportOptions.slackChannel || ''}
                    onChange={(e) => 
                      setExportOptions(prev => ({ ...prev, slackChannel: e.target.value }))
                    }
                  />
                )}
              </div>
            </div>
          </div>

          {/* Notes */}
          <div>
            <Label htmlFor="notes">Additional Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Add any additional context or notes for this export..."
              value={exportOptions.notes || ''}
              onChange={(e) => 
                setExportOptions(prev => ({ ...prev, notes: e.target.value }))
              }
              className="mt-1"
            />
          </div>

          {/* Export Button */}
          <Button 
            onClick={handleExport} 
            disabled={isExporting}
            className="w-full"
            size="lg"
          >
            {isExporting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export as {exportOptions.format.toUpperCase()}
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Export History */}
      {exportJobs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Export History</CardTitle>
            <CardDescription>Recent export jobs and downloads</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {exportJobs.map((job) => (
                <div key={job.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getFormatIcon(job.format)}
                    <div>
                      <p className="font-medium">{job.format.toUpperCase()} Export</p>
                      <p className="text-sm text-gray-500">
                        {job.createdAt.toLocaleString()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    {getStatusIcon(job.status)}
                    <Badge variant={
                      job.status === 'completed' ? 'default' :
                      job.status === 'failed' ? 'destructive' : 'secondary'
                    }>
                      {job.status}
                    </Badge>
                    
                    {job.status === 'completed' && job.downloadUrl && (
                      <Button size="sm" variant="outline" onClick={() => downloadFile(job)}>
                        <Download className="h-4 w-4 mr-1" />
                        Download
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
