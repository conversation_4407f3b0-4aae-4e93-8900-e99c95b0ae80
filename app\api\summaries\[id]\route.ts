import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
// Removed Supabase import - using dev-only mode

/**
 * GET /api/summaries/[id]
 * Get a specific summary by ID - Production Demo Mode
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: summaryId } = await params;
    console.log('📄 Get Summary API called - Production Demo Mode:', summaryId);

    if (!summaryId) {
      return NextResponse.json(
        { error: 'Summary ID is required' },
        { status: 400 }
      );
    }

    // Get authenticated user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Check if this is one of our predefined demo summaries
    const predefinedSummaries = {
      'demo-summary-1-12345678-1234-1234-1234-123456789012': {
        id: 'demo-summary-1-12345678-1234-1234-1234-123456789012',
        title: 'Q4 Engineering Sprint Planning',
        content: 'Team discussed upcoming Q4 sprint goals, resource allocation, and technical debt priorities. Key decisions made on microservices migration timeline and performance optimization targets. The team reviewed current sprint velocity and identified dependencies that could impact delivery timelines. Architecture decisions were made regarding the migration from monolithic to microservices architecture, with a focus on maintaining system reliability during the transition.',
        summary_data: {
          summary: 'Engineering team aligned on Q4 priorities including microservices migration and performance improvements',
          key_points: [
            'Microservices migration timeline set for Q4',
            'Performance optimization targets defined',
            'Resource allocation approved',
            'Technical debt prioritization completed',
            'Sprint velocity analysis conducted'
          ],
          action_items: [
            'Create detailed migration roadmap',
            'Set up performance monitoring dashboard',
            'Schedule architecture review sessions',
            'Allocate resources for Q4 sprint',
            'Define success metrics for migration'
          ],
          participants: 8,
          duration: '45 minutes',
          sentiment: 'positive'
        },
        source: 'slack',
        slack_channel: 'engineering',
        slack_message_ts: '1703123456.789012',
        metadata: {
          ai_model: 'deepseek-r1',
          processing_time: 2340,
          confidence: 0.92,
          message_count: 34,
          thread_count: 5
        },
        tags: ['engineering', 'planning', 'q4', 'microservices', 'architecture'],
        created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        user_id: user.id,
        organization_id: user.id
      },
      'demo-summary-2-12345678-1234-1234-1234-123456789012': {
        id: 'demo-summary-2-12345678-1234-1234-1234-123456789012',
        title: 'Product Roadmap Review - Mobile App Features',
        content: 'Product team reviewed mobile app feature requests, user feedback analysis, and competitive landscape. Prioritized push notifications, offline mode, and enhanced search functionality. The team analyzed user engagement metrics and identified key areas for improvement in the mobile experience.',
        summary_data: {
          summary: 'Product roadmap updated with mobile-first features based on user feedback and competitive analysis',
          key_points: [
            'Push notifications identified as top priority',
            'Offline mode development approved for Q1',
            'Enhanced search functionality scoped',
            'User feedback analysis completed',
            'Competitive landscape assessment conducted'
          ],
          action_items: [
            'Create detailed mobile feature specifications',
            'Conduct additional user interviews',
            'Prototype offline functionality',
            'Design push notification system',
            'Plan search enhancement implementation'
          ],
          participants: 6,
          duration: '60 minutes',
          sentiment: 'enthusiastic'
        },
        source: 'slack',
        slack_channel: 'product',
        slack_message_ts: '1703109876.543210',
        metadata: {
          ai_model: 'gpt-4o-mini',
          processing_time: 1890,
          confidence: 0.89,
          message_count: 28,
          thread_count: 3
        },
        tags: ['product', 'mobile', 'roadmap', 'features', 'user-feedback'],
        created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        user_id: user.id,
        organization_id: user.id
      },
      'demo-summary-3-12345678-1234-1234-1234-123456789012': {
        id: 'demo-summary-3-12345678-1234-1234-1234-123456789012',
        title: 'Marketing Campaign Performance Analysis',
        content: 'Marketing team analyzed Q3 campaign performance across all channels. Social media campaigns showed 34% increase in engagement, email campaigns had 12% higher open rates, and paid ads achieved 2.3x ROAS. The analysis included detailed breakdowns by channel, audience segment, and campaign type.',
        summary_data: {
          summary: 'Q3 marketing campaigns exceeded targets with strong performance across all channels',
          key_points: [
            'Social media engagement increased by 34%',
            'Email open rates improved by 12%',
            'Paid advertising achieved 2.3x ROAS',
            'Cross-channel attribution analysis completed',
            'Audience segmentation optimization identified'
          ],
          action_items: [
            'Scale successful social media campaigns',
            'Optimize email template designs',
            'Increase paid advertising budget allocation',
            'Implement cross-channel tracking',
            'Develop audience segmentation strategy'
          ],
          participants: 5,
          duration: '30 minutes',
          sentiment: 'celebratory'
        },
        source: 'upload',
        file_url: 'https://demo-storage.supabase.co/marketing-analysis-q3.pdf',
        metadata: {
          ai_model: 'deepseek-r1',
          processing_time: 3120,
          confidence: 0.94,
          file_type: 'pdf',
          pages: 12,
          file_size: '2.4 MB'
        },
        tags: ['marketing', 'analysis', 'q3', 'performance', 'campaigns'],
        created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        user_id: user.id,
        organization_id: user.id
      }
    };

    // Check if it's a predefined summary
    const summary = predefinedSummaries[summaryId as keyof typeof predefinedSummaries];
    
    if (summary) {
      console.log('📄 Returning predefined demo summary:', summary.title);
      return NextResponse.json({
        success: true,
        data: summary
      });
    }

    // If not predefined, check if it's a dynamically created demo summary
    if (summaryId.startsWith('demo-summary-')) {
      console.log('📄 Returning dynamic demo summary:', summaryId);
      
      // Generate a realistic dynamic summary
      const dynamicSummary = {
        id: summaryId,
        title: 'Generated Demo Summary',
        content: 'This is a dynamically generated demo summary for testing purposes. It demonstrates the full functionality of the summary system including AI processing, tagging, and export capabilities.',
        summary_data: {
          summary: 'Dynamically generated demo content showcasing system capabilities',
          key_points: [
            'Demo summary successfully created',
            'All features working as expected',
            'Ready for production deployment'
          ],
          action_items: [
            'Review demo functionality',
            'Test export capabilities',
            'Validate user experience'
          ],
          participants: 1,
          duration: 'instant',
          sentiment: 'positive'
        },
        source: 'demo',
        metadata: {
          ai_model: 'demo-generator',
          processing_time: 100,
          confidence: 1.0
        },
        tags: ['demo', 'generated', 'test'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: user.id,
        organization_id: user.id
      };

      return NextResponse.json({
        success: true,
        data: dynamicSummary
      });
    }

    // Summary not found
    return NextResponse.json(
      { error: 'Summary not found' },
      { status: 404 }
    );

  } catch (error) {
    console.error('Get summary API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/summaries/[id]
 * Update a specific summary - Production Demo Mode
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: summaryId } = await params;
    console.log('📄 Update Summary API called - Production Demo Mode:', summaryId);

    const body = await request.json();
    const { title, content, tags } = body;

    if (!summaryId) {
      return NextResponse.json(
        { error: 'Summary ID is required' },
        { status: 400 }
      );
    }

    // Production demo mode - simulate update
    console.log('📄 Demo summary updated:', summaryId);

    return NextResponse.json({
      success: true,
      message: 'Summary updated successfully',
      data: {
        id: summaryId,
        title: title || 'Updated Demo Summary',
        content: content || 'Updated content',
        tags: tags || [],
        updated_at: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Update summary API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/summaries/[id]
 * Delete a specific summary - Production Demo Mode
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: summaryId } = await params;
    console.log('📄 Delete Summary API called - Production Demo Mode:', summaryId);

    if (!summaryId) {
      return NextResponse.json(
        { error: 'Summary ID is required' },
        { status: 400 }
      );
    }

    // Production demo mode - simulate deletion
    console.log('📄 Demo summary deleted:', summaryId);

    return NextResponse.json({
      success: true,
      message: 'Summary deleted successfully'
    });

  } catch (error) {
    console.error('Delete summary API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
