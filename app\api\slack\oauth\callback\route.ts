import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { getCurrentUser } from '@/lib/user-management';

export async function GET(request: NextRequest) {
  try {
    console.log('🔗 Slack OAuth callback');

    // Get authenticated user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const userId = user.id;

    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    // Handle OAuth errors
    if (error) {
      console.error('Slack OAuth error:', error);
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?error=oauth_failed`);
    }

    if (!code) {
      console.error('Missing authorization code');
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?error=missing_code`);
    }

    // Verify state parameter (basic security check)
    if (state && !state.startsWith(userId)) {
      console.error('Invalid state parameter');
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?error=invalid_state`);
    }

    console.log('🔗 Processing OAuth callback for user:', userId);

    // Exchange code for access token
    const slackClientId = process.env.SLACK_CLIENT_ID;
    const slackClientSecret = process.env.SLACK_CLIENT_SECRET;
    const redirectUri = `${process.env.NEXT_PUBLIC_SITE_URL}/api/slack/oauth/callback`;

    if (!slackClientId || !slackClientSecret) {
      console.error('Missing Slack credentials');
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?error=config_error`);
    }

    const tokenResponse = await fetch('https://slack.com/api/oauth.v2.access', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        code,
        client_id: slackClientId,
        client_secret: slackClientSecret,
        redirect_uri: redirectUri,
      }),
    });

    if (!tokenResponse.ok) {
      console.error('Slack token exchange failed:', await tokenResponse.text());
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?error=token_exchange_failed`);
    }

    const tokenData = await tokenResponse.json();

    if (!tokenData.ok) {
      console.error('Slack API error:', tokenData.error);
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?error=slack_api_error`);
    }

    // Extract relevant data
    const {
      access_token,
      team,
      authed_user,
      scope,
      bot_user_id,
    } = tokenData;

    // Create Supabase client
    const supabase = await createSupabaseServerClient();

    // Store the integration in the database
    const { data: integration, error: integrationError } = await supabase
      .from('slack_integrations')
      .upsert({
        user_id: userId,
        team_id: team.id,
        team_name: team.name,
        access_token,
        scope,
        bot_user_id,
        authed_user_id: authed_user.id,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (integrationError) {
      console.error('Failed to store Slack integration:', integrationError);
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?error=storage_failed`);
    }

    // Create a notification for the user
    await supabase
      .from('notifications')
      .insert({
        user_id: userId,
        type: 'slack_connected',
        title: '🎉 Slack Connected Successfully',
        message: `Your Slack workspace "${team.name}" has been connected. You can now generate summaries from your Slack conversations.`,
        data: {
          team_id: team.id,
          team_name: team.name,
        },
      });

    // Auto-complete onboarding step if applicable
    try {
      await supabase.rpc('complete_onboarding_step', {
        p_user_id: userId,
        p_step_name: 'connect_slack',
        p_step_data: { 
          team_id: team.id,
          team_name: team.name,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.warn('Failed to complete onboarding step:', error);
      // Non-critical error, continue
    }

    console.log('✅ Slack integration successful:', {
      userId,
      teamId: team.id,
      teamName: team.name
    });

    // Redirect back to settings with success message
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?success=slack_connected&team=${encodeURIComponent(team.name)}`);

  } catch (error) {
    console.error('Slack OAuth callback error:', error);

    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings?error=internal_error`);
  }
}
