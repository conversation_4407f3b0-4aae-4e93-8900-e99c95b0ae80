import { NextRequest, NextResponse } from 'next/server';
import { getAvailableAIModels } from '@/lib/ai-routing-service';
import { AI_MODELS, canUseModel } from '@/lib/ai-models';
// Removed Supabase import - using dev-only mode
// Removed: import { SentryTracker } from '@/lib/sentry.client';
import { getCurrentUser } from '@/lib/user-management';

/**
 * GET /api/ai/models
 * Get available AI models for user's subscription plan
 */
export async function GET(request: NextRequest) {
  try {
    // Public Demo Mode - no authentication required
    const user = await getCurrentUser();
  if (!user) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }
  const userId = user.id;

    const result = await getAvailableAIModels(userId);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      models: result.models,
      subscription_tier: result.subscription_tier,
    });

  } catch (error) {
    console.error('Error fetching AI models:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));

    return NextResponse.json(
      { error: 'Failed to fetch AI models' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/ai/models
 * Update user's AI model preferences
 */
export async function PUT(request: NextRequest) {
  try {
    const { 
      preferred_model,
      organization_id 
    } = await request.json();

    if (!preferred_model) {
      return NextResponse.json(
        { error: 'Preferred model is required' },
        { status: 400 }
      );
    }

    // Validate model exists
    if (!AI_MODELS[preferred_model]) {
      return NextResponse.json(
        { error: 'Invalid AI model' },
        { status: 400 }
      );
    }

    // Get current user (dev mode)
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user's organization if not provided (dev mode)
    let orgId = organization_id;
    if (!orgId) {
      // DEV MODE: Use user's default org
      orgId = user.orgId || 'org-dev-001';
    }

    // Get user's subscription plan (dev mode)
    const userPlan = (user.plan?.toUpperCase() || 'FREE') as 'FREE' | 'PRO' | 'ENTERPRISE';

    // Check if user can use the preferred model
    if (!canUseModel(userPlan, preferred_model)) {
      return NextResponse.json(
        { 
          error: 'Model not available for your subscription plan',
          required_plan: AI_MODELS[preferred_model].requiredPlan,
          current_plan: userPlan
        },
        { status: 403 }
      );
    }

    // Update user settings (dev mode - no database operation)
    console.log('📄 Dev mode: Skipping user settings update for AI model preference');
    const data = {
      user_id: user.id,
      organization_id: orgId,
      preferred_ai_model: preferred_model,
      updated_at: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      message: 'AI model preference updated successfully',
      data: {
        preferred_model: data.preferred_ai_model,
        model_info: AI_MODELS[preferred_model]
      }
    });

  } catch (error) {
    console.error('Update AI model preference API error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/ai/models
 * Test AI model with sample text
 */
export async function POST(request: NextRequest) {
  try {
    const { 
      model_id,
      sample_text,
      organization_id 
    } = await request.json();

    if (!model_id || !sample_text) {
      return NextResponse.json(
        { error: 'Model ID and sample text are required' },
        { status: 400 }
      );
    }

    // Get current user (dev mode)
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user's subscription plan (dev mode)
    const subscription = { plan: user.plan || 'FREE' };

    const userPlan = (subscription?.plan?.toUpperCase() || 'FREE') as 'FREE' | 'PRO' | 'ENTERPRISE';

    // Check if user can use the model
    if (!canUseModel(userPlan, model_id)) {
      return NextResponse.json(
        { 
          error: 'Model not available for your subscription plan',
          required_plan: AI_MODELS[model_id].requiredPlan,
          current_plan: userPlan
        },
        { status: 403 }
      );
    }

    // Import and use the AI generation function
    const { generateAISummary } = await import('@/lib/ai-models');
    
    // Generate sample summary
    const result = await generateAISummary(
      sample_text,
      model_id,
      'This is a test summarization to demonstrate model capabilities.'
    );

    return NextResponse.json({
      success: true,
      data: {
        model_used: result.model,
        summary: result.text,
        tokens_used: result.tokens,
        cost: result.cost,
        processing_time_ms: result.processingTime,
        quality_scores: result.qualityScores
      }
    });

  } catch (error) {
    console.error('Test AI model API error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
