import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';

export async function GET(request: NextRequest) {
  try {
    console.log('🔗 Slack OAuth initiation');

    // Public Demo Mode - no authentication required
    const user = await getCurrentUser();
  if (!user) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }
  const userId = user.id;

    // Slack OAuth configuration
    const clientId = process.env.SLACK_CLIENT_ID;
    const redirectUri = `${process.env.NEXT_PUBLIC_SITE_URL}/api/slack/oauth/callback`;
    
    if (!clientId) {
      console.error('❌ Slack OAuth: Missing SLACK_CLIENT_ID');
      return NextResponse.json(
        { success: false, error: 'Slack OAuth not configured' },
        { status: 500 }
      );
    }

    // Generate state parameter for security
    const state = `${userId}-${Date.now()}-${Math.random().toString(36).substring(7)}`;

    // Slack OAuth scopes
    const scopes = [
      'channels:read',
      'channels:history',
      'groups:read',
      'groups:history',
      'im:read',
      'im:history',
      'mpim:read',
      'mpim:history',
      'users:read',
      'team:read',
      'chat:write',
      'files:read'
    ].join(',');

    // Build Slack OAuth URL
    const slackOAuthUrl = new URL('https://slack.com/oauth/v2/authorize');
    slackOAuthUrl.searchParams.set('client_id', clientId);
    slackOAuthUrl.searchParams.set('scope', scopes);
    slackOAuthUrl.searchParams.set('redirect_uri', redirectUri);
    slackOAuthUrl.searchParams.set('state', state);
    slackOAuthUrl.searchParams.set('user_scope', 'identity.basic,identity.email');

    console.log('🔗 Redirecting to Slack OAuth:', slackOAuthUrl.toString());

    // Store state in session/database for verification
    // For now, we'll include it in the redirect and verify in callback

    // Redirect to Slack OAuth
    return NextResponse.redirect(slackOAuthUrl.toString());

  } catch (error) {
    console.error('❌ Slack OAuth error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
