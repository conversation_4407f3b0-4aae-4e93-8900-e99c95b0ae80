'use client';

// Disable static generation for this page
export const dynamic = 'force-dynamic';
export const revalidate = 0;

import React, { useState, useEffect } from 'react';
import { getCurrentUserClient } from '@/lib/user-management-client';
import AuthGuard from '@/components/AuthGuard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Settings,
  Brain,
  Building2,
  Crown,
  User as UserIcon,
  Bell,
  Shield,
  Zap,
  TrendingUp,
  MessageSquare,
  ExternalLink,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';

export default function SettingsPage() {
  const [user, setUser] = useState<any>(null);
  const [authLoading, setAuthLoading] = useState(true);
  const [userPlan, setUserPlan] = useState<'FREE' | 'PRO' | 'ENTERPRISE'>('FREE');
  const [selectedAIModel, setSelectedAIModel] = useState('deepseek-r1');
  const [showOnboarding, setShowOnboarding] = useState(true);
  const [slackConnected, setSlackConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Check authentication on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const currentUser = await getCurrentUserClient();
        if (!currentUser) {
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/login';
          }
          return;
        }
        setUser(currentUser);
      } catch (error) {
        console.error('Authentication check failed:', error);
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login';
        }
      } finally {
        setAuthLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Load user settings on mount
  useEffect(() => {
    if (user) {
      // Load user preferences from Supabase
      loadUserSettings();
      // Check URL parameters for OAuth callback messages
      checkOAuthCallback();
    }
  }, [user]);

  const checkOAuthCallback = () => {
    if (typeof window === 'undefined') return;

    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const error = urlParams.get('error');
    const team = urlParams.get('team');

    if (success === 'slack_connected') {
      toast.success(`Slack workspace "${team}" connected successfully!`);
      setSlackConnected(true);
      // Clean up URL
      window.history.replaceState({}, '', '/dashboard/settings');
    } else if (error) {
      const errorMessages = {
        unauthorized: 'Please sign in to connect Slack',
        oauth_failed: 'Slack authorization failed',
        missing_code: 'Authorization code missing',
        invalid_state: 'Invalid authorization state',
        config_error: 'Server configuration error',
        token_exchange_failed: 'Failed to exchange authorization code',
        slack_api_error: 'Slack API error',
        storage_failed: 'Failed to save integration',
        internal_error: 'Internal server error'
      };
      const message = errorMessages[error as keyof typeof errorMessages] || 'Unknown error occurred';
      toast.error(`Failed to connect Slack: ${message}`);
      // Clean up URL
      window.history.replaceState({}, '', '/dashboard/settings');
    }
  };

  const loadUserSettings = async () => {
    try {
      setIsLoading(true);

      // Check for existing Slack integration
      const response = await fetch('/api/slack/status');
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.connected) {
          setSlackConnected(true);
        }
      }

      console.log('Loading user settings for:', user?.id);
    } catch (error) {
      console.error('Error loading user settings:', error);
      toast.error('Failed to load settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpgradeClick = (requiredPlan: string) => {
    // Redirect to billing page for plan management
    if (typeof window !== 'undefined') {
      window.location.href = '/billing';
    }
  };

  const handleModelSelect = (modelId: string) => {
    setSelectedAIModel(modelId);
    console.log('AI model selected:', modelId);
    // TODO: Save to Supabase
  };

  const handleSlackConnect = async () => {
    try {
      setIsLoading(true);
      // Redirect to Slack OAuth
      const slackOAuthUrl = `/api/slack/oauth?user_id=${user?.id}`;
      if (typeof window !== 'undefined') {
        window.location.href = slackOAuthUrl;
      }
    } catch (error) {
      console.error('Error connecting to Slack:', error);
      toast.error('Failed to connect to Slack');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSlackDisconnect = async () => {
    try {
      setIsLoading(true);
      // TODO: Remove Slack integration from Supabase
      setSlackConnected(false);
      toast.success('Slack disconnected successfully');
    } catch (error) {
      console.error('Error disconnecting Slack:', error);
      toast.error('Failed to disconnect Slack');
    } finally {
      setIsLoading(false);
    }
  };

  const getPlanBadge = () => {
    const planConfig = {
      FREE: { color: 'bg-blue-100 text-blue-800', icon: <Zap className="h-3 w-3" /> },
      PRO: { color: 'bg-purple-100 text-purple-800', icon: <Crown className="h-3 w-3" /> },
      ENTERPRISE: { color: 'bg-yellow-100 text-yellow-800', icon: <Crown className="h-3 w-3" /> }
    };

    const config = planConfig[userPlan];
    return (
      <Badge className={`${config.color} flex items-center space-x-1`}>
        {config.icon}
        <span>{userPlan}</span>
      </Badge>
    );
  };

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="h-48 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
      <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center space-x-3">
              <Settings className="h-8 w-8" />
              <span>Settings</span>
            </h1>
            <p className="text-gray-600 mt-2">
              Manage your AI models, integrations, and account preferences
            </p>
          </div>
          <div className="flex items-center space-x-3">
            {getPlanBadge()}
            <Button onClick={() => handleUpgradeClick('PRO')} className="bg-purple-600 hover:bg-purple-700">
              <Crown className="h-4 w-4 mr-2" />
              Upgrade Plan
            </Button>
          </div>
        </div>
      </div>

      {/* Onboarding Checklist */}
      {showOnboarding && (
        <div className="mb-8">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">Getting Started</h3>
            <p className="text-blue-700 mb-4">Complete these steps to get the most out of Slack Summary Scribe:</p>
            <ul className="space-y-2 text-sm text-blue-600">
              <li>✓ Connect your Slack workspace</li>
              <li>✓ Configure AI summarization preferences</li>
              <li>✓ Set up notification preferences</li>
            </ul>
            <button
              onClick={() => setShowOnboarding(false)}
              className="mt-4 text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Dismiss
            </button>
          </div>
        </div>
      )}

      {/* Settings Tabs */}
      <Tabs defaultValue="ai-models" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="ai-models" className="flex items-center space-x-2">
            <Brain className="h-4 w-4" />
            <span>AI Models</span>
          </TabsTrigger>
          <TabsTrigger value="slack" className="flex items-center space-x-2">
            <MessageSquare className="h-4 w-4" />
            <span>Slack</span>
          </TabsTrigger>
          <TabsTrigger value="integrations" className="flex items-center space-x-2">
            <Building2 className="h-4 w-4" />
            <span>CRM</span>
          </TabsTrigger>
          <TabsTrigger value="account" className="flex items-center space-x-2">
            <UserIcon className="h-4 w-4" />
            <span>Account</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center space-x-2">
            <Bell className="h-4 w-4" />
            <span>Notifications</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center space-x-2">
            <Shield className="h-4 w-4" />
            <span>Security</span>
          </TabsTrigger>
        </TabsList>

        {/* AI Models Tab */}
        <TabsContent value="ai-models" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="h-5 w-5" />
                <span>AI Model Selection</span>
              </CardTitle>
              <CardDescription>
                Choose your preferred AI model for summarization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">DeepSeek R1</h4>
                    <p className="text-sm text-gray-600">Fast and efficient summarization</p>
                  </div>
                  <Badge variant="default">Current</Badge>
                </div>
                <div className="flex items-center justify-between p-4 border rounded-lg opacity-50">
                  <div>
                    <h4 className="font-medium">GPT-4o-mini</h4>
                    <p className="text-sm text-gray-600">Premium model with advanced features</p>
                  </div>
                  <Badge variant="outline">Pro Plan</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>AI Usage Analytics</span>
              </CardTitle>
              <CardDescription>
                Track your AI model usage and performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="text-2xl font-bold text-blue-900">127</div>
                  <div className="text-sm text-blue-700">Summaries Generated</div>
                </div>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="text-2xl font-bold text-green-900">84%</div>
                  <div className="text-sm text-green-700">Average Quality Score</div>
                </div>
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <div className="text-2xl font-bold text-purple-900">$2.45</div>
                  <div className="text-sm text-purple-700">Total AI Costs</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Slack Tab */}
        <TabsContent value="slack" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MessageSquare className="h-5 w-5" />
                <span>Slack Integration</span>
              </CardTitle>
              <CardDescription>
                Connect your Slack workspace to automatically summarize conversations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {!slackConnected ? (
                <div className="text-center py-8">
                  <MessageSquare className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Connect to Slack</h3>
                  <p className="text-gray-600 mb-6 max-w-md mx-auto">
                    Connect your Slack workspace to automatically generate summaries from your conversations and meetings.
                  </p>
                  <Button
                    onClick={handleSlackConnect}
                    disabled={isLoading}
                    className="bg-[#4A154B] hover:bg-[#350d36] text-white"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    {isLoading ? 'Connecting...' : 'Connect to Slack'}
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <div>
                        <div className="font-medium text-green-900">Slack Connected</div>
                        <div className="text-sm text-green-700">Your workspace is connected and ready</div>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      onClick={handleSlackDisconnect}
                      disabled={isLoading}
                      className="text-red-600 border-red-200 hover:bg-red-50"
                    >
                      Disconnect
                    </Button>
                  </div>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Auto-Summary Settings</CardTitle>
                      <CardDescription>
                        Configure when and how summaries are automatically generated
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">Auto-summarize meetings</div>
                            <div className="text-sm text-gray-600">Automatically create summaries for scheduled meetings</div>
                          </div>
                          <input type="checkbox" className="h-4 w-4" defaultChecked />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">Thread summaries</div>
                            <div className="text-sm text-gray-600">Summarize long thread conversations</div>
                          </div>
                          <input type="checkbox" className="h-4 w-4" defaultChecked />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">Daily digest</div>
                            <div className="text-sm text-gray-600">Send daily summary of important conversations</div>
                          </div>
                          <input type="checkbox" className="h-4 w-4" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* CRM Integrations Tab */}
        <TabsContent value="integrations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building2 className="h-5 w-5" />
                <span>CRM Integrations</span>
              </CardTitle>
              <CardDescription>
                Connect your CRM systems for enhanced workflow automation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                      <Building2 className="h-4 w-4 text-orange-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">HubSpot</h4>
                      <p className="text-sm text-gray-600">Sync contacts and deals</p>
                    </div>
                  </div>
                  <Button variant="outline" size="sm">Connect</Button>
                </div>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Building2 className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">Salesforce</h4>
                      <p className="text-sm text-gray-600">Enterprise CRM integration</p>
                    </div>
                  </div>
                  <Button variant="outline" size="sm">Connect</Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Export Settings</CardTitle>
              <CardDescription>
                Configure how summaries are exported to your CRM systems
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Default Export Type</label>
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option value="note">Note/Activity</option>
                    <option value="contact">Contact</option>
                    <option value="deal">Deal/Opportunity</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Auto-Export</label>
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option value="manual">Manual Only</option>
                    <option value="auto">Auto-Export All</option>
                    <option value="high-quality">Auto-Export High Quality Only</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Account Tab */}
        <TabsContent value="account" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
              <CardDescription>
                Manage your account details and subscription
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={user?.email || ''}
                    className="mt-1"
                    readOnly
                  />
                </div>
                <div>
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    type="text"
                    value={user?.name || ''}
                    className="mt-1"
                    readOnly
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="user-id">User ID</Label>
                  <Input
                    id="user-id"
                    type="text"
                    value={user?.id || ''}
                    className="mt-1 font-mono text-xs"
                    readOnly
                  />
                </div>
                <div>
                  <Label htmlFor="created">Account Created</Label>
                  <Input
                    id="created"
                    type="text"
                    value={user?.created_at ? new Date(user.created_at).toLocaleDateString() : ''}
                    className="mt-1"
                    readOnly
                  />
                </div>
              </div>

              <div className="pt-4 border-t">
                <h3 className="font-medium mb-2">Subscription Details</h3>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center space-x-2">
                      <span>Current Plan:</span>
                      {getPlanBadge()}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      {userPlan === 'FREE' ? 'Free plan with basic features' : 'Premium plan with advanced features'}
                    </div>
                  </div>
                  <Button onClick={() => handleUpgradeClick('PRO')}>
                    {userPlan === 'FREE' ? 'Upgrade' : 'Manage Billing'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notifications Tab */}
        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>
                Choose how you want to be notified about summaries and updates
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                {[
                  { id: 'email', label: 'Email Notifications', description: 'Receive email alerts for new summaries' },
                  { id: 'slack', label: 'Slack Notifications', description: 'Get notified in Slack when summaries are ready' },
                  { id: 'browser', label: 'Browser Notifications', description: 'Show browser notifications for real-time updates' },
                  { id: 'weekly', label: 'Weekly Summary', description: 'Receive a weekly digest of your AI usage' }
                ].map(notification => (
                  <div key={notification.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                    <div>
                      <div className="font-medium">{notification.label}</div>
                      <div className="text-sm text-gray-600">{notification.description}</div>
                    </div>
                    <input type="checkbox" className="h-4 w-4" defaultChecked />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>
                Manage your account security and data privacy
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg bg-gray-50">
                  <div>
                    <div className="font-medium">Two-Factor Authentication</div>
                    <div className="text-sm text-gray-600">Add an extra layer of security to your account</div>
                  </div>
                  <Button variant="outline">Configure</Button>
                </div>
                
                <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  <div>
                    <div className="font-medium">Data Export</div>
                    <div className="text-sm text-gray-600">Download all your data and summaries</div>
                  </div>
                  <Button variant="outline">Export Data</Button>
                </div>
                
                <div className="flex items-center justify-between p-3 border border-red-200 rounded-lg bg-red-50">
                  <div>
                    <div className="font-medium text-red-900">Delete Account</div>
                    <div className="text-sm text-red-700">Permanently delete your account and all data</div>
                  </div>
                  <Button variant="destructive">Delete</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
  );
}
