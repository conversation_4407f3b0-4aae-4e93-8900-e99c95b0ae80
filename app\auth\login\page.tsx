'use client';

/**
 * Dev Login Page - No Authentication Required
 *
 * Automatically logs in with dev user for development
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowR<PERSON>, Loader2, User, Zap, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useDevAuth, DEV_USERS } from '@/lib/dev-auth';
import Link from 'next/link';

export default function DevLoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { user, login } = useDevAuth();

  // Auto-redirect if already logged in
  useEffect(() => {
    if (user) {
      router.push('/dashboard');
    }
  }, [user, router]);

  const handleDevLogin = async (userIndex: number) => {
    setIsLoading(true);
    const devUser = DEV_USERS[userIndex];

    try {
      await login(devUser.email, 'dev-password');
      router.push('/dashboard');
    } catch (error) {
      console.error('Dev login failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickLogin = async () => {
    await handleDevLogin(0); // Default to first dev user
  };

  if (user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <Shield className="w-6 h-6 text-green-600" />
            </div>
            <CardTitle>Already Logged In</CardTitle>
            <CardDescription>Redirecting to dashboard...</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Zap className="w-6 h-6 text-blue-600" />
          </div>
          <CardTitle className="text-2xl font-bold">Dev Mode Login</CardTitle>
          <CardDescription>
            No authentication required - choose a dev user to continue
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Quick Login Button */}
          <Button
            onClick={handleQuickLogin}
            disabled={isLoading}
            className="w-full h-12 text-base"
            size="lg"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <ArrowRight className="w-4 h-4 mr-2" />
            )}
            Quick Login (Default User)
          </Button>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white px-2 text-muted-foreground">Or choose user</span>
            </div>
          </div>

          {/* Dev User Selection */}
          <div className="space-y-2">
            {DEV_USERS.map((devUser, index) => (
              <Button
                key={devUser.id}
                variant="outline"
                onClick={() => handleDevLogin(index)}
                disabled={isLoading}
                className="w-full justify-start h-auto p-4"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-white" />
                  </div>
                  <div className="text-left">
                    <div className="font-medium">{devUser.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {devUser.email} • {devUser.plan} plan
                    </div>
                  </div>
                </div>
              </Button>
            ))}
          </div>

          {/* Dev Info */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <Zap className="w-4 h-4 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium">Development Mode</p>
                <p className="text-xs mt-1">
                  No real authentication required. All features are available for testing.
                </p>
              </div>
            </div>
          </div>
        </CardContent>

        <div className="px-6 pb-6">
          <div className="text-center text-sm text-muted-foreground">
            Need to test signup? {' '}
            <Link href="/auth/signup" className="text-blue-600 hover:underline">
              Go to dev signup
            </Link>
          </div>
        </div>
      </Card>
    </div>
  );
}


