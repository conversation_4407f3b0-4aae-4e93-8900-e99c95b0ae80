/**
 * Supabase Browser Client for Production Mode
 *
 * Provides client-side Supabase client for database and storage operations
 * Works in public access mode without authentication requirements
 */

'use client'

import { createClient } from '@supabase/supabase-js';

let supabaseClient: any = null;

/**
 * Create or get existing Supabase browser client
 */
export function createBrowserSupabaseClient() {
  if (supabaseClient) {
    return supabaseClient;
  }

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.warn('Supabase credentials not configured, using fallback client');
    // Return a mock client that doesn't throw errors
    return {
      from: () => ({
        select: () => ({ data: [], error: null }),
        insert: () => ({ data: null, error: null }),
        update: () => ({ data: null, error: null }),
        delete: () => ({ data: null, error: null }),
      }),
      storage: {
        from: () => ({
          upload: () => ({ data: null, error: null }),
          download: () => ({ data: null, error: null }),
        })
      }
    };
  }

  supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: false, // No auth sessions needed for public access
    },
  });

  return supabaseClient;
}

/**
 * Get OAuth redirect URL (not used in public mode)
 */
export function getOAuthRedirectUrl() {
  return `${window.location.origin}/auth/callback`;
}

/**
 * Validate client session (always returns valid for public mode)
 */
export function validateClientSession() {
  return { isValid: true, user: null };
}

/**
 * Create realtime subscription (optional for public mode)
 */
export function createRealtimeSubscription(table: string, callback: Function) {
  const client = createBrowserSupabaseClient();

  if (!client.channel) {
    console.warn('Realtime not available, using polling fallback');
    return null;
  }

  return client
    .channel(`public:${table}`)
    .on('postgres_changes', { event: '*', schema: 'public', table }, callback)
    .subscribe();
}


