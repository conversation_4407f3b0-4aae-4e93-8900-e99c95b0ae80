'use client';

import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Download, Share2, Calendar, FileText, Zap, Star, Crown } from 'lucide-react';
import { toast } from 'sonner';
import AuthGuard from '@/components/AuthGuard';
import ChunkErrorBoundary from '@/components/ChunkErrorBoundary';

interface SummaryData {
  id: string;
  title: string;
  content: string;
  source_type: string;
  source_data?: {
    channel_name?: string;
    message_count?: number;
    participants?: string[];
    filename?: string;
    file_size?: number;
  };
  created_at: string;
  user_id: string;
  ai_model: string;
  quality_score: number;
}

function SummaryPageContent() {
  const params = useParams();
  const router = useRouter();
  const [summary, setSummary] = useState<SummaryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const summaryId = params?.id as string;

  useEffect(() => {
    const fetchSummary = async () => {
      try {
        setLoading(true);
        setError(null);

        // For demo mode, generate summary data based on ID
        console.log('📄 Fetching summary for ID:', summaryId);
        
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Generate demo summary data
        const demoSummary: SummaryData = {
          id: summaryId,
          title: getDemoTitle(summaryId),
          content: getDemoContent(summaryId),
          source_type: getDemoSourceType(summaryId),
          source_data: getDemoSourceData(summaryId),
          created_at: new Date().toISOString(),
          user_id: 'demo-user',
          ai_model: getDemoAIModel(summaryId),
          quality_score: 0.85 + Math.random() * 0.1 // 0.85-0.95
        };

        setSummary(demoSummary);
      } catch (err) {
        console.error('Error fetching summary:', err);
        setError('Failed to load summary');
      } finally {
        setLoading(false);
      }
    };

    if (summaryId) {
      fetchSummary();
    }
  }, [summaryId]);

  const getDemoTitle = (id: string): string => {
    if (id.includes('upload')) {
      return '8 tips for men To get clear skin @Themasterminds7.pdf';
    }
    const titles = [
      'Team Standup Summary',
      'Product Planning Meeting',
      'Engineering Discussion',
      'Marketing Strategy Session',
      'Customer Feedback Review'
    ];
    return titles[Math.abs(id.split('').reduce((a, b) => a + b.charCodeAt(0), 0)) % titles.length];
  };

  const getDemoContent = (id: string): string => {
    if (id.includes('upload')) {
      return `# Document Summary: 8 Tips for Men to Get Clear Skin

## Key Points:

1. **Daily Cleansing Routine**: Use a gentle, non-comedogenic cleanser twice daily to remove dirt, oil, and impurities without over-drying the skin.

2. **Moisturize Regularly**: Apply a lightweight, oil-free moisturizer to maintain skin hydration and prevent excessive oil production.

3. **Sun Protection**: Use broad-spectrum SPF 30+ sunscreen daily to prevent UV damage and premature aging.

4. **Healthy Diet**: Incorporate foods rich in antioxidants, omega-3 fatty acids, and vitamins A, C, and E. Limit dairy and high-glycemic foods.

5. **Stay Hydrated**: Drink at least 8 glasses of water daily to help flush toxins and maintain skin elasticity.

6. **Quality Sleep**: Aim for 7-9 hours of sleep nightly to allow skin repair and regeneration.

7. **Stress Management**: Practice stress-reduction techniques like meditation or exercise, as stress can trigger acne breakouts.

8. **Avoid Touching Face**: Keep hands away from face to prevent transferring bacteria and oils that can clog pores.

## Additional Recommendations:
- Consider using products with salicylic acid or benzoyl peroxide for acne-prone skin
- Change pillowcases regularly to reduce bacterial buildup
- Consult a dermatologist for persistent skin issues

**Document processed successfully with AI-powered summarization.**`;
    }

    const contents = [
      `# Team Standup Summary

## Key Discussion Points:
- Sprint progress review: 85% completion rate
- Identified 3 blockers requiring immediate attention
- Resource allocation for upcoming features
- Code review backlog reduction strategies

## Action Items:
1. Backend team to resolve API performance issues by Friday
2. Frontend team to complete responsive design updates
3. QA team to prioritize testing for release candidate

## Next Steps:
- Daily check-ins on blocker resolution
- Sprint retrospective scheduled for Thursday
- Demo preparation for stakeholder meeting`,

      `# Product Planning Meeting

## Q4 Roadmap Decisions:
- Feature prioritization based on user feedback
- Resource allocation across 3 development teams
- Timeline estimation for major releases
- Integration requirements with third-party services

## Key Outcomes:
- Approved 5 high-priority features for development
- Established clear success metrics for each initiative
- Defined go-to-market strategy for new features

## Follow-up Actions:
- Technical specification documents due next week
- User research sessions to validate assumptions
- Competitive analysis update required`,

      `# Engineering Discussion

## Technical Architecture Review:
- System scalability improvements
- Performance optimization strategies
- Deployment pipeline enhancements
- Security audit recommendations

## Decisions Made:
- Migration to microservices architecture approved
- Implementation of automated testing framework
- Adoption of new monitoring and alerting tools

## Implementation Timeline:
- Phase 1: Infrastructure setup (2 weeks)
- Phase 2: Service migration (4 weeks)
- Phase 3: Performance testing (1 week)`
    ];

    return contents[Math.abs(id.split('').reduce((a, b) => a + b.charCodeAt(0), 0)) % contents.length];
  };

  const getDemoSourceType = (id: string): string => {
    if (id.includes('upload')) return 'file_upload';
    return Math.random() > 0.5 ? 'slack' : 'manual_input';
  };

  const getDemoSourceData = (id: string) => {
    if (id.includes('upload')) {
      return {
        filename: '8 tips for men To get clear skin @Themasterminds7.pdf',
        file_size: 887689 // bytes
      };
    }

    if (getDemoSourceType(id) === 'slack') {
      const channels = ['#general', '#product', '#engineering', '#marketing'];
      return {
        channel_name: channels[Math.abs(id.split('').reduce((a, b) => a + b.charCodeAt(0), 0)) % channels.length],
        message_count: 15 + Math.floor(Math.random() * 30),
        participants: ['john.doe', 'jane.smith', 'mike.wilson', 'sarah.johnson'].slice(0, 2 + Math.floor(Math.random() * 3))
      };
    }

    return {};
  };

  const getDemoAIModel = (id: string): string => {
    const models = ['deepseek-r1', 'gpt-4o-mini', 'claude-3-5-sonnet'];
    return models[Math.abs(id.split('').reduce((a, b) => a + b.charCodeAt(0), 0)) % models.length];
  };

  const getAIModelIcon = (model: string) => {
    switch (model) {
      case 'deepseek-r1':
        return <Zap className="h-4 w-4 text-blue-500" />;
      case 'gpt-4o-mini':
        return <Star className="h-4 w-4 text-purple-500" />;
      case 'claude-3-5-sonnet':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const handleDownload = () => {
    if (!summary) return;
    
    const content = `# ${summary.title}\n\n${summary.content}\n\n---\nGenerated by SummaryAI on ${new Date(summary.created_at).toLocaleDateString()}`;
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${summary.title.replace(/[^a-zA-Z0-9]/g, '_')}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Summary downloaded successfully!');
  };

  const handleShare = async () => {
    if (!summary) return;
    
    try {
      await navigator.clipboard.writeText(window.location.href);
      toast.success('Summary link copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy link');
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <Skeleton className="h-10 w-32 mb-4" />
            <Skeleton className="h-8 w-96 mb-2" />
            <Skeleton className="h-4 w-64" />
          </div>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error || !summary) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-4xl mx-auto">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-6"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <Card>
            <CardContent className="text-center py-12">
              <h2 className="text-xl font-semibold mb-2">Summary Not Found</h2>
              <p className="text-gray-600 mb-4">
                {error || 'The summary you\'re looking for doesn\'t exist or has been removed.'}
              </p>
              <Button onClick={() => router.push('/dashboard')}>
                Go to Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {summary.title}
              </h1>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>{new Date(summary.created_at).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center space-x-1">
                  {getAIModelIcon(summary.ai_model)}
                  <span>{summary.ai_model}</span>
                </div>
                <Badge variant="secondary">
                  Quality: {(summary.quality_score * 100).toFixed(0)}%
                </Badge>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleShare}>
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownload}>
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            </div>
          </div>
        </div>

        {/* Source Information */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg">Source Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Source Type</label>
                <p className="text-sm capitalize">{summary.source_type.replace('_', ' ')}</p>
              </div>
              
              {summary.source_data?.channel_name && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Slack Channel</label>
                  <p className="text-sm">{summary.source_data.channel_name}</p>
                </div>
              )}
              
              {summary.source_data?.filename && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Filename</label>
                  <p className="text-sm">{summary.source_data.filename}</p>
                </div>
              )}
              
              {summary.source_data?.file_size && (
                <div>
                  <label className="text-sm font-medium text-gray-600">File Size</label>
                  <p className="text-sm">{formatFileSize(summary.source_data.file_size)}</p>
                </div>
              )}
              
              {summary.source_data?.message_count && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Messages Processed</label>
                  <p className="text-sm">{summary.source_data.message_count}</p>
                </div>
              )}
              
              {summary.source_data?.participants && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Participants</label>
                  <p className="text-sm">{summary.source_data.participants.join(', ')}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Summary Content */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Summary Content</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose max-w-none">
              <pre className="whitespace-pre-wrap font-sans text-sm leading-relaxed">
                {summary.content}
              </pre>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function SummaryPage() {
  return (
    <ChunkErrorBoundary>
      <AuthGuard>
        <SummaryPageContent />
      </AuthGuard>
    </ChunkErrorBoundary>
  );
}
