import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { getCurrentUser } from '@/lib/user-management';

export async function GET(request: NextRequest) {
  try {
    console.log('📄 Summaries API called - Production Mode');

    // Get authenticated user
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log(`📄 Fetching summaries for user: ${user.id}`);

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const search = searchParams.get('search');

    // DEV MODE: Return mock summaries data
    const summariesData = [
      {
        id: 'demo-summary-1',
        user_id: user.id,
        title: 'Demo Summary - Team Meeting',
        content: 'This is a demo summary of a team meeting discussing project updates and next steps.',
        source_type: 'manual',
        created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        updated_at: new Date(Date.now() - 86400000).toISOString(),
        source_data: {
          model: 'deepseek-r1',
          tokens: 150,
          cost: 0.001
        }
      },
      {
        id: 'demo-summary-2',
        user_id: user.id,
        title: 'Demo Summary - Product Review',
        content: 'This is a demo summary of a product review session with key feedback and action items.',
        source_type: 'upload',
        created_at: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
        updated_at: new Date(Date.now() - 172800000).toISOString(),
        source_data: {
          model: 'deepseek-r1',
          tokens: 200,
          cost: 0.002
        }
      }
    ];

    console.log('📄 Dev mode: Returning mock summaries data');

    console.log(`📄 Found ${summariesData?.length || 0} summaries for user ${user.id}`);

    // Transform data to match frontend interface
    const transformedSummaries = (summariesData || []).map((summary: any) => ({
      id: summary.id,
      title: summary.title || 'Untitled Summary',
      content: summary.content || summary.summary_text || '',
      keyPoints: summary.key_points || summary.summary?.keyPoints || [],
      actionItems: summary.action_items || summary.summary?.actions || [],
      participants: summary.participants || summary.summary?.participants || [],
      duration: summary.duration,
      createdAt: summary.created_at,
      updatedAt: summary.updated_at,
      status: summary.status || 'completed',
      source: summary.source_type || 'manual',
      workspaceId: summary.workspace_id,
      workspaceName: summary.workspace_name,
      channelName: summary.slack_channel ? `#${summary.slack_channel}` : undefined,
      userId: summary.user_id,
      tags: Array.isArray(summary.tags) ? summary.tags : []
    }));

    return NextResponse.json({
      success: true,
      data: transformedSummaries,
      total: transformedSummaries.length,
      limit,
      offset,
      totalPages: Math.ceil(transformedSummaries.length / limit)
    });


  } catch (error) {
    console.error('Summaries API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('📄 Create Summary API called - Production Mode');

    // Get authenticated user
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { title, content, source, source_data, tags } = body;

    if (!title || !content) {
      return NextResponse.json(
        { error: 'Missing required fields: title, content' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabase = await createSupabaseServerClient();

    // Insert summary into database
    const { data: summary, error: summaryError } = await supabase
      .from('summaries')
      .insert({
        user_id: user.id,
        title,
        content,
        source_type: source || 'manual',
        source_data: source_data || {},
        tags: tags || []
      })
      .select()
      .single();

    if (summaryError) {
      console.error('Error creating summary:', summaryError);
      return NextResponse.json(
        { error: 'Failed to create summary' },
        { status: 500 }
      );
    }

    console.log('📄 Summary created:', summary.id);

    return NextResponse.json({
      success: true,
      data: summary,
      message: 'Summary created successfully'
    });

  } catch (error) {
    console.error('Create summary API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
