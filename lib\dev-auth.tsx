/**
 * DEV-ONLY AUTHENTICATION MOCK
 * 
 * This replaces all Supabase authentication with hardcoded dev users
 * for local development without requiring any external auth services.
 */

'use client';

import React, { useState, useEffect, createContext, useContext } from 'react';

// Dev User Types
export interface DevUser {
  id: string;
  email: string;
  name: string;
  orgId: string;
  orgName: string;
  plan: 'free' | 'pro' | 'enterprise';
  avatar?: string;
  createdAt: string;
}

export interface DevAuthContext {
  user: DevUser | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  signup: (email: string, password: string, name: string) => Promise<void>;
}

// Single default dev user for simplicity
export const DEFAULT_DEV_USER: DevUser = {
  id: 'dev-user-001',
  email: '<EMAIL>',
  name: 'Dev User',
  orgId: 'org-dev-001',
  orgName: 'Dev Organization',
  plan: 'pro',
  avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=dev',
  createdAt: new Date().toISOString(),
};

// Multiple dev users for testing (optional)
export const DEV_USERS: DevUser[] = [
  DEFAULT_DEV_USER,
  {
    id: 'dev-user-002',
    email: '<EMAIL>',
    name: 'Admin User',
    orgId: 'org-dev-002',
    orgName: 'Admin Organization',
    plan: 'enterprise',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
    createdAt: new Date().toISOString(),
  },
  {
    id: 'dev-user-003',
    email: '<EMAIL>',
    name: 'Free User',
    orgId: 'org-dev-003',
    orgName: 'Free Organization',
    plan: 'free',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=free',
    createdAt: new Date().toISOString(),
  },
];

// Create context
const DevAuthContext = createContext<DevAuthContext | null>(null);

// Simplified Dev Auth Provider
export function DevAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<DevUser | null>(DEFAULT_DEV_USER);
  const [isLoading, setIsLoading] = useState(false);

  // Simplified login - just sets the user immediately
  const login = async (email: string, password: string) => {
    setIsLoading(true);

    // Find user by email or use default
    const foundUser = DEV_USERS.find(u => u.email === email) || DEFAULT_DEV_USER;

    setUser(foundUser);
    setIsLoading(false);
  };

  // Simplified logout - just resets to default user
  const logout = async () => {
    setUser(DEFAULT_DEV_USER);
  };

  // Simplified signup - just logs in with default user
  const signup = async (email: string, password: string, name: string) => {
    setUser(DEFAULT_DEV_USER);
  };

  return (
    <DevAuthContext.Provider value={{ user, isLoading, login, logout, signup }}>
      {children}
    </DevAuthContext.Provider>
  );
}

// Dev Auth Hook
export function useDevAuth(): DevAuthContext {
  const context = useContext(DevAuthContext);
  if (!context) {
    throw new Error('useDevAuth must be used within DevAuthProvider');
  }
  return context;
}

// Simplified utility functions to replace Supabase auth
export const getCurrentUser = async (): Promise<DevUser | null> => {
  // Always return the default dev user
  return DEFAULT_DEV_USER;
};

export const getCurrentUserClient = async (): Promise<DevUser | null> => {
  return getCurrentUser();
};

// Mock session check
export const getSession = async () => {
  const user = await getCurrentUser();
  return {
    data: {
      session: user ? {
        user: user,
        access_token: 'dev-token',
        refresh_token: 'dev-refresh-token',
      } : null
    },
    error: null
  };
};

// Dev users are already exported above

// Public access mode check (works in both dev and production)
export const isPublicMode = () => {
  return process.env.NEXT_PUBLIC_MODE === 'production' || process.env.NEXT_PUBLIC_DEV_MODE === 'false' || true;
};

console.log('🚀 Public Auth initialized - No authentication required for public access');
