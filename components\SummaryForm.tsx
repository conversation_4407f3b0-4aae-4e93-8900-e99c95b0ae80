/**
 * Summary Form Component
 * 
 * Form for creating new summaries from meeting transcripts
 */

'use client';

import { useState, useCallback, useEffect } from 'react';
import { useDebouncedSubmit } from '@/utils/fetch-utils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Zap, 
  Upload, 
  AlertCircle, 
  CheckCircle,
  Loader2,
  Sparkles
} from 'lucide-react';
import { getCurrentUserClient } from '@/lib/user-management-client';

export interface SummaryResult {
  id: string;
  title: string;
  content: string;
  keyPoints: string[];
  actionItems: string[];
  participants: string[];
  duration?: string;
  createdAt: string;
  status: 'completed' | 'processing' | 'failed';
}

interface SummaryFormProps {
  onSummaryCreated?: (summary: SummaryResult) => void;
  onError?: (error: string) => void;
  className?: string;
  placeholder?: string;
  maxLength?: number;
}

export function SummaryForm({ 
  onSummaryCreated,
  onError,
  className = '',
  placeholder = "Paste your meeting transcript here...",
  maxLength = 10000
}: SummaryFormProps) {
  const [user, setUser] = useState<any>(null);
  const [transcriptText, setTranscriptText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<SummaryResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Get current user on mount
  useEffect(() => {
    const loadUser = async () => {
      try {
        const currentUser = await getCurrentUserClient();
        setUser(currentUser);
      } catch (error) {
        console.error('Failed to load user:', error);
      }
    };
    loadUser();
  }, []);

  // Debounced fetch to prevent rapid-fire submissions
  const { debouncedFetch, isDebouncing } = useDebouncedSubmit(
    async () => {
      const response = await fetch('/api/summarize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transcriptText: transcriptText.trim(),
          userId: user?.id,
          context: {
            source: 'manual',
            timestamp: new Date().toISOString(),
          }
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate summary');
      }

      // Handle API response structure
      if (result.success && result.data) {
        return result.data;
      }

      return result;
    },
    1000 // 1 second debounce
  );

  const handleSubmit = useCallback(async () => {
    if (!transcriptText.trim()) {
      const errorMsg = 'Please enter a transcript to summarize';
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    if (transcriptText.length > maxLength) {
      const errorMsg = `Transcript is too long. Maximum ${maxLength} characters allowed.`;
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    setIsProcessing(true);
    setError(null);
    setResult(null);

    try {
      const apiResponse = await debouncedFetch();

      if (apiResponse) {
        // Transform API response to SummaryResult format
        const summaryResult: SummaryResult = {
          id: apiResponse.id || `summary-${Date.now()}`,
          title: apiResponse.title || `Summary - ${new Date().toLocaleDateString()}`,
          content: apiResponse.summary_text || apiResponse.content || '',
          keyPoints: apiResponse.summary?.skills || [],
          actionItems: apiResponse.summary?.actions || [],
          participants: apiResponse.summary?.participants || [],
          duration: apiResponse.processing_time_ms ? `${apiResponse.processing_time_ms}ms` : undefined,
          createdAt: apiResponse.created_at || new Date().toISOString(),
          status: 'completed'
        };

        setResult(summaryResult);
        onSummaryCreated?.(summaryResult);

        // Clear the form after successful submission
        setTranscriptText('');
      }

    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to generate summary';
      setError(errorMsg);
      onError?.(errorMsg);
    } finally {
      setIsProcessing(false);
    }
  }, [transcriptText, maxLength, debouncedFetch, onSummaryCreated, onError]);

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'text/plain' && !file.name.endsWith('.txt')) {
      setError('Please upload a text file (.txt)');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      if (content.length > maxLength) {
        setError(`File is too large. Maximum ${maxLength} characters allowed.`);
        return;
      }
      setTranscriptText(content);
      setError(null);
    };
    reader.readAsText(file);
  }, [maxLength]);

  const characterCount = transcriptText.length;
  const isNearLimit = characterCount > maxLength * 0.8;
  const isOverLimit = characterCount > maxLength;

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="h-8 w-8 rounded-lg bg-blue-100 flex items-center justify-center">
              <Sparkles className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <CardTitle className="text-lg">Create Summary</CardTitle>
              <CardDescription>
                Generate AI-powered summaries from your meeting transcripts
              </CardDescription>
            </div>
          </div>
          <Badge variant="secondary" className="text-xs">
            AI Powered
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* File Upload */}
        <div className="flex items-center space-x-2">
          <label htmlFor="file-upload" className="cursor-pointer">
            <Button variant="outline" size="sm" asChild>
              <span>
                <Upload className="h-4 w-4 mr-2" />
                Upload File
              </span>
            </Button>
          </label>
          <input
            id="file-upload"
            type="file"
            accept=".txt"
            onChange={handleFileUpload}
            className="hidden"
          />
          <span className="text-xs text-gray-500">or paste text below</span>
        </div>

        {/* Transcript Input */}
        <div className="space-y-2">
          <Textarea
            value={transcriptText}
            onChange={(e) => setTranscriptText(e.target.value)}
            placeholder={placeholder}
            className={`min-h-[120px] resize-none ${isOverLimit ? 'border-red-500' : ''}`}
            disabled={isProcessing}
          />
          
          {/* Character Count */}
          <div className="flex justify-between items-center text-xs">
            <span className={`${isNearLimit ? 'text-orange-500' : isOverLimit ? 'text-red-500' : 'text-gray-500'}`}>
              {characterCount.toLocaleString()} / {maxLength.toLocaleString()} characters
            </span>
            {transcriptText && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setTranscriptText('')}
                className="h-auto p-1 text-xs"
              >
                Clear
              </Button>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Success Display */}
        {result && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              Summary created successfully! Check the recent summaries below.
            </AlertDescription>
          </Alert>
        )}

        {/* Submit Button */}
        <Button
          onClick={handleSubmit}
          disabled={!transcriptText.trim() || isProcessing || isOverLimit || isDebouncing}
          className="w-full"
          size="lg"
        >
          {isProcessing ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Generating Summary...
            </>
          ) : isDebouncing ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <Zap className="h-4 w-4 mr-2" />
              Generate Summary
            </>
          )}
        </Button>

        {/* Processing Info */}
        {isProcessing && (
          <div className="text-center text-sm text-gray-500">
            <p>AI is analyzing your transcript...</p>
            <p className="text-xs mt-1">This usually takes 10-30 seconds</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Compact version of the summary form
 */
interface CompactSummaryFormProps extends Omit<SummaryFormProps, 'className'> {
  className?: string;
}

export function CompactSummaryForm(props: CompactSummaryFormProps) {
  return (
    <SummaryForm
      {...props}
      className={`max-w-2xl ${props.className || ''}`}
      placeholder="Quick summary: paste your transcript here..."
    />
  );
}
