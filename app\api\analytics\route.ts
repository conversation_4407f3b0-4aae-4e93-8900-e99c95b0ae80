/**
 * Analytics API Routes
 * Provides user and team analytics data
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import {
  getUserAnalytics,
  getTeamAnalytics,
  trackUsageEvent,
  exportAnalytics,
  type AnalyticsFilters,
} from '@/lib/analytics-service';
import { withSubscriptionCheck } from '@/lib/subscription-middleware';
// Removed: import { SentryTracker } from '@/lib/sentry.client';

/**
 * GET /api/analytics
 * Get user or team analytics
 */
export async function GET(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const { searchParams } = new URL(req.url);
      const type = searchParams.get('type') || 'user'; // 'user' or 'team'
      const period = (searchParams.get('period') || 'weekly') as 'daily' | 'weekly' | 'monthly';
      const startDate = searchParams.get('start_date');
      const endDate = searchParams.get('end_date');
      const organizationId = searchParams.get('organization_id') || user.id;

      const filters: AnalyticsFilters = {
        period,
        start_date: startDate || undefined,
        end_date: endDate || undefined,
        organization_id: organizationId,
      };

      if (type === 'team') {
        // Team analytics requires Enterprise subscription
        if (context.subscription.tier !== 'ENTERPRISE') {
          return NextResponse.json(
            {
              error: 'Team analytics requires Enterprise subscription',
              upgrade_required: true,
              current_tier: context.subscription.tier,
            },
            { status: 403 }
          );
        }

        const result = await getTeamAnalytics(organizationId, filters);

        if (!result.success) {
          return NextResponse.json(
            { error: result.error },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          type: 'team',
          analytics: result.analytics,
        });
      }

      // User analytics
      const result = await getUserAnalytics(context.userId, filters);

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        type: 'user',
        analytics: result.analytics,
      });

    } catch (error) {
      console.error('Analytics API error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));

      return NextResponse.json(
        { error: 'Failed to fetch analytics' },
        { status: 500 }
      );
    }
  });
}

/**
 * POST /api/analytics
 * Track usage event
 */
export async function POST(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const body = await req.json();
      const { event_type, ai_model, tokens_used, cost, metadata } = body;

      if (!event_type) {
        return NextResponse.json(
          { error: 'event_type is required' },
          { status: 400 }
        );
      }

      const validEventTypes = ['summary_created', 'ai_request', 'export', 'crm_sync', 'slack_post'];
      if (!validEventTypes.includes(event_type)) {
        return NextResponse.json(
          { error: `Invalid event_type. Must be one of: ${validEventTypes.join(', ')}` },
          { status: 400 }
        );
      }

      const result = await trackUsageEvent(context.userId, user.id, {
        event_type,
        ai_model,
        tokens_used,
        cost,
        metadata,
      });

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'Usage event tracked successfully',
      });

    } catch (error) {
      console.error('Track usage event error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));

      return NextResponse.json(
        { error: 'Failed to track usage event' },
        { status: 500 }
      );
    }
  });
}




