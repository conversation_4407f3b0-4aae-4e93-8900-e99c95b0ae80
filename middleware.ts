import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Handle static asset fallbacks with comprehensive error handling
  if (pathname.startsWith('/icons/') || pathname === '/favicon.ico' || pathname.startsWith('/apple-touch-icon')) {
    try {
      // Favicon fallback
      if (pathname === '/favicon.ico') {
        return NextResponse.rewrite(new URL('/favicon.svg', request.url));
      }

      // Apple touch icon fallback
      if (pathname.includes('apple-touch-icon')) {
        return NextResponse.rewrite(new URL('/apple-touch-icon.png', request.url));
      }

      // Icon fallbacks
      if (pathname.includes('icon-144x144.png')) {
        return NextResponse.rewrite(new URL('/icons/icon-144x144.png', request.url));
      }

      // Generic icon fallback
      if (pathname.startsWith('/icons/') && pathname.endsWith('.png')) {
        return NextResponse.rewrite(new URL('/placeholder.svg', request.url));
      }
    } catch (error) {
      console.warn('Static asset fallback error:', error);
      // Return a minimal SVG as ultimate fallback
      return new NextResponse(
        '<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><rect width="32" height="32" fill="#ddd"/></svg>',
        {
          status: 200,
          headers: {
            'Content-Type': 'image/svg+xml',
            'Cache-Control': 'public, max-age=3600'
          }
        }
      );
    }
  }

  // Handle API routes - ensure no auth required
  if (pathname.startsWith('/api/')) {
    // Add CORS headers for API routes
    const response = NextResponse.next();
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return new Response(null, { status: 200, headers: response.headers });
    }
    
    return response;
  }

  // Handle public routes - no authentication required
  const publicRoutes = [
    '/',
    '/dashboard',
    '/upload',
    '/summaries',
    '/support',
    '/pricing',
    '/privacy',
    '/terms'
  ];

  const isPublicRoute = publicRoutes.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  );

  if (isPublicRoute) {
    // Allow access to all public routes
    return NextResponse.next();
  }

  // Default: allow access (public mode)
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
