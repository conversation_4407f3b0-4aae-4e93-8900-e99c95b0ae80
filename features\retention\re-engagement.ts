/**
 * Re-engagement Campaign System
 * 
 * Automated user re-engagement through personalized email campaigns
 */

import { createSupabaseServerClient } from '@/lib/supabase-server';

export interface ReEngagementCampaign {
  id: string;
  userId: string;
  campaignType: string;
  status: 'scheduled' | 'sent' | 'opened' | 'clicked' | 'converted';
  scheduledFor: string;
  sentAt?: string;
  openedAt?: string;
  clickedAt?: string;
  convertedAt?: string;
  metadata: Record<string, any>;
  createdAt: string;
}

export interface UserEngagement {
  userId: string;
  organizationId?: string;
  lastActiveAt: string;
  totalSessions: number;
  totalTimeSpent: number;
  featuresUsed: Record<string, number>;
  onboardingCompleted: boolean;
  onboardingStep: number;
  engagementScore: number;
  riskLevel: 'low' | 'medium' | 'high';
}

export interface CampaignTemplate {
  type: string;
  subject: string;
  content: string;
  triggerDays: number;
  targetSegment: string;
  personalizedFields: string[];
}

// Pre-defined campaign templates
const CAMPAIGN_TEMPLATES: CampaignTemplate[] = [
  {
    type: 'inactive_7_days',
    subject: 'We miss you! Your team summaries are waiting',
    content: `Hi {{firstName}},

We noticed you haven't been active in Slack Summary Scribe for a week. Your team {{teamName}} has been generating valuable insights that you might find interesting.

Here's what you've missed:
- {{missedSummaries}} new summaries created
- {{teamActivity}} team activities
- New features: {{newFeatures}}

Ready to catch up? Click here to see your latest summaries: {{dashboardLink}}

Best regards,
The Slack Summary Scribe Team`,
    triggerDays: 7,
    targetSegment: 'inactive_users',
    personalizedFields: ['firstName', 'teamName', 'missedSummaries', 'teamActivity', 'newFeatures', 'dashboardLink']
  },
  {
    type: 'inactive_14_days',
    subject: 'Don\'t let valuable insights slip away',
    content: `Hi {{firstName}},

It's been two weeks since we last saw you in Slack Summary Scribe. We understand you're busy, but we wanted to share some quick wins that could save you time:

✅ Set up automated daily summaries (2 minutes)
✅ Connect your most active Slack channels
✅ Export summaries to share with your team

Your {{planName}} plan includes {{planFeatures}} that your team {{teamName}} could benefit from.

Quick setup: {{quickSetupLink}}

Need help? Reply to this email and we'll get you set up personally.

Best,
{{senderName}}`,
    triggerDays: 14,
    targetSegment: 'at_risk_users',
    personalizedFields: ['firstName', 'planName', 'planFeatures', 'teamName', 'quickSetupLink', 'senderName']
  },
  {
    type: 'inactive_30_days',
    subject: 'Last chance: Your account will be downgraded soon',
    content: `Hi {{firstName}},

We haven't seen you in Slack Summary Scribe for 30 days. We'd hate to see you go!

Before we downgrade your {{planName}} account, we wanted to offer:

🎁 50% off your next month
📞 Free 15-minute setup call with our team
🚀 Priority access to new features

Your team {{teamName}} generated {{totalSummaries}} summaries while you were away. There might be valuable insights waiting for you.

Claim your offer: {{offerLink}}

If you're no longer interested, you can unsubscribe here: {{unsubscribeLink}}

We hope to see you back soon!
{{senderName}}`,
    triggerDays: 30,
    targetSegment: 'churned_users',
    personalizedFields: ['firstName', 'planName', 'teamName', 'totalSummaries', 'offerLink', 'unsubscribeLink', 'senderName']
  }
];

/**
 * Check for users needing re-engagement
 */
export async function checkUsersForReEngagement(): Promise<{
  processed: number;
  campaignsCreated: number;
}> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Get users with their last activity
    const { data: users, error } = await supabase
      .from('user_engagement')
      .select(`
        *,
        profiles!user_engagement_user_id_fkey (
          email,
          full_name
        )
      `)
      .lt('last_active_at', new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString()); // 6+ days ago

    if (error || !users) {
      console.error('Failed to get users for re-engagement:', error);
      return { processed: 0, campaignsCreated: 0 };
    }

    let campaignsCreated = 0;

    for (const user of users) {
      const daysSinceActive = Math.floor(
        (Date.now() - new Date(user.last_active_at).getTime()) / (24 * 60 * 60 * 1000)
      );

      // Determine appropriate campaign type
      let campaignType: string | null = null;
      
      if (daysSinceActive >= 30) {
        campaignType = 'inactive_30_days';
      } else if (daysSinceActive >= 14) {
        campaignType = 'inactive_14_days';
      } else if (daysSinceActive >= 7) {
        campaignType = 'inactive_7_days';
      }

      if (campaignType) {
        // Check if campaign already exists
        const { data: existingCampaign } = await supabase
          .from('re_engagement_campaigns')
          .select('id')
          .eq('user_id', user.user_id)
          .eq('campaign_type', campaignType)
          .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
          .single();

        if (!existingCampaign) {
          await createReEngagementCampaign(user, campaignType);
          campaignsCreated++;
        }
      }
    }

    return { processed: users.length, campaignsCreated };

  } catch (error) {
    console.error('Failed to check users for re-engagement:', error);
    return { processed: 0, campaignsCreated: 0 };
  }
}

/**
 * Create re-engagement campaign
 */
async function createReEngagementCampaign(
  user: any,
  campaignType: string
): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Schedule campaign for optimal time (9 AM user's timezone, or 9 AM UTC)
    const scheduledFor = new Date();
    scheduledFor.setHours(9, 0, 0, 0);
    if (scheduledFor <= new Date()) {
      scheduledFor.setDate(scheduledFor.getDate() + 1);
    }

    // Get personalization data
    const personalizationData = await getPersonalizationData(user);

    await supabase
      .from('re_engagement_campaigns')
      .insert({
        user_id: user.user_id,
        campaign_type: campaignType,
        status: 'scheduled',
        scheduled_for: scheduledFor.toISOString(),
        metadata: {
          personalization: personalizationData,
          engagementScore: user.engagement_score,
          riskLevel: user.risk_level,
          daysSinceActive: Math.floor(
            (Date.now() - new Date(user.last_active_at).getTime()) / (24 * 60 * 60 * 1000)
          )
        },
        created_at: new Date().toISOString()
      });

  } catch (error) {
    console.error('Failed to create re-engagement campaign:', error);
  }
}

/**
 * Get personalization data for user
 */
async function getPersonalizationData(user: any): Promise<Record<string, any>> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Get user's organization and team info
    const { data: orgMembership } = await supabase
      .from('user_organizations')
      .select(`
        organizations (
          name,
          plan
        )
      `)
      .eq('user_id', user.user_id)
      .single();

    // Get recent summaries count
    const { data: summaries } = await supabase
      .from('summaries')
      .select('id')
      .eq('user_id', user.user_id)
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

    // Get team activity
    const { data: teamActivity } = await supabase
      .from('summaries')
      .select('id')
      .eq('organization_id', user.organization_id)
      .gte('created_at', new Date(user.last_active_at).toISOString());

    return {
      firstName: user.profiles?.full_name?.split(' ')[0] || 'there',
      teamName: (orgMembership?.organizations as any)?.name || 'your team',
      planName: (orgMembership?.organizations as any)?.plan || 'Free',
      totalSummaries: summaries?.length || 0,
      missedSummaries: teamActivity?.length || 0,
      teamActivity: `${teamActivity?.length || 0} new activities`,
      newFeatures: 'AI-powered insights, Export improvements',
      dashboardLink: `${process.env.NEXT_PUBLIC_SITE_URL}/dashboard`,
      quickSetupLink: `${process.env.NEXT_PUBLIC_SITE_URL}/onboarding`,
      offerLink: `${process.env.NEXT_PUBLIC_SITE_URL}/billing?offer=comeback50`,
      unsubscribeLink: `${process.env.NEXT_PUBLIC_SITE_URL}/unsubscribe`,
      senderName: 'Sarah from Slack Summary Scribe'
    };

  } catch (error) {
    console.error('Failed to get personalization data:', error);
    return {
      firstName: 'there',
      teamName: 'your team',
      planName: 'Free'
    };
  }
}

/**
 * Process scheduled campaigns
 */
export async function processScheduledCampaigns(): Promise<{
  processed: number;
  sent: number;
  failed: number;
}> {
  try {
    const supabase = await createSupabaseServerClient();
    
    // Get campaigns ready to send
    const { data: campaigns, error } = await supabase
      .from('re_engagement_campaigns')
      .select(`
        *,
        profiles!re_engagement_campaigns_user_id_fkey (
          email,
          full_name
        )
      `)
      .eq('status', 'scheduled')
      .lte('scheduled_for', new Date().toISOString())
      .limit(50); // Process in batches

    if (error || !campaigns) {
      console.error('Failed to get scheduled campaigns:', error);
      return { processed: 0, sent: 0, failed: 0 };
    }

    let sent = 0;
    let failed = 0;

    for (const campaign of campaigns) {
      try {
        await sendReEngagementEmail(campaign);
        
        // Update campaign status
        await supabase
          .from('re_engagement_campaigns')
          .update({
            status: 'sent',
            sent_at: new Date().toISOString()
          })
          .eq('id', campaign.id);
        
        sent++;
      } catch (error) {
        console.error(`Failed to send campaign ${campaign.id}:`, error);
        failed++;
      }
    }

    return { processed: campaigns.length, sent, failed };

  } catch (error) {
    console.error('Failed to process scheduled campaigns:', error);
    return { processed: 0, sent: 0, failed: 0 };
  }
}

/**
 * Send re-engagement email
 */
async function sendReEngagementEmail(campaign: any): Promise<void> {
  const template = CAMPAIGN_TEMPLATES.find(t => t.type === campaign.campaign_type);
  if (!template) {
    throw new Error(`Template not found for campaign type: ${campaign.campaign_type}`);
  }

  // Personalize email content
  let personalizedSubject = template.subject;
  let personalizedContent = template.content;

  Object.entries(campaign.metadata.personalization || {}).forEach(([key, value]) => {
    const placeholder = `{{${key}}}`;
    personalizedSubject = personalizedSubject.replace(new RegExp(placeholder, 'g'), String(value));
    personalizedContent = personalizedContent.replace(new RegExp(placeholder, 'g'), String(value));
  });

  // Send email using your email service (Resend)
  // This would integrate with your actual email sending logic
  console.log('Sending re-engagement email:', {
    to: campaign.profiles.email,
    subject: personalizedSubject,
    content: personalizedContent
  });

  // In production, you would use Resend or your email service:
  /*
  await resend.emails.send({
    from: '<EMAIL>',
    to: campaign.profiles.email,
    subject: personalizedSubject,
    html: personalizedContent
  });
  */
}

/**
 * Track campaign engagement
 */
export async function trackCampaignEngagement(
  campaignId: string,
  engagementType: 'opened' | 'clicked' | 'converted'
): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const updateData: any = {
      status: engagementType,
      [`${engagementType}_at`]: new Date().toISOString()
    };

    await supabase
      .from('re_engagement_campaigns')
      .update(updateData)
      .eq('id', campaignId);

  } catch (error) {
    console.error('Failed to track campaign engagement:', error);
  }
}

/**
 * Get re-engagement analytics
 */
export async function getReEngagementAnalytics(
  dateRange: { start: string; end: string }
): Promise<{
  totalCampaigns: number;
  sentCampaigns: number;
  openRate: number;
  clickRate: number;
  conversionRate: number;
  campaignsByType: Record<string, number>;
}> {
  try {
    const supabase = await createSupabaseServerClient();
    
    const { data: campaigns, error } = await supabase
      .from('re_engagement_campaigns')
      .select('*')
      .gte('created_at', dateRange.start)
      .lte('created_at', dateRange.end);

    if (error || !campaigns) {
      return {
        totalCampaigns: 0,
        sentCampaigns: 0,
        openRate: 0,
        clickRate: 0,
        conversionRate: 0,
        campaignsByType: {}
      };
    }

    const totalCampaigns = campaigns.length;
    const sentCampaigns = campaigns.filter(c => c.status !== 'scheduled').length;
    const openedCampaigns = campaigns.filter(c => c.opened_at).length;
    const clickedCampaigns = campaigns.filter(c => c.clicked_at).length;
    const convertedCampaigns = campaigns.filter(c => c.converted_at).length;

    const campaignsByType = campaigns.reduce((acc, campaign) => {
      acc[campaign.campaign_type] = (acc[campaign.campaign_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalCampaigns,
      sentCampaigns,
      openRate: sentCampaigns > 0 ? (openedCampaigns / sentCampaigns) * 100 : 0,
      clickRate: sentCampaigns > 0 ? (clickedCampaigns / sentCampaigns) * 100 : 0,
      conversionRate: sentCampaigns > 0 ? (convertedCampaigns / sentCampaigns) * 100 : 0,
      campaignsByType
    };

  } catch (error) {
    console.error('Failed to get re-engagement analytics:', error);
    return {
      totalCampaigns: 0,
      sentCampaigns: 0,
      openRate: 0,
      clickRate: 0,
      conversionRate: 0,
      campaignsByType: {}
    };
  }
}
