'use client';

import { useEffect } from 'react';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Enhanced global error logging
    console.error('🚨 GLOBAL ERROR CAUGHT:', error);
    console.error('🔍 Error message:', error.message);
    console.error('📋 Error stack:', error.stack);
    console.error('🆔 Error digest:', error.digest);
    console.error('🕐 Error time:', new Date().toISOString());
    console.error('🌐 Current URL:', typeof window !== 'undefined' ? window.location.href : 'SSR');

    // Error logging (Sentry removed to prevent crashes)
    if (typeof window !== 'undefined') {
      // Simple error logging without external dependencies
      console.error('🔥 Global error logged for debugging');
    }

    // Check for chunk loading errors and auto-recover
    const errorMessage = error?.message || '';
    if (errorMessage.includes('Cannot read properties of undefined (reading \'call\')') ||
        errorMessage.includes('ChunkLoadError') ||
        errorMessage.includes('Loading chunk') ||
        errorMessage.includes('runtime.js')) {

      console.log('🔄 Chunk loading error detected, clearing caches and reloading...');

      // Clear all caches and reload
      if (typeof window !== 'undefined') {
        Promise.all([
          // Clear service worker caches
          'caches' in window ? caches.keys().then(names =>
            Promise.all(names.map(name => caches.delete(name)))
          ) : Promise.resolve(),
          // Clear localStorage
          localStorage.clear(),
          // Clear sessionStorage
          sessionStorage.clear()
        ]).finally(() => {
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        });
      }
    }
  }, [error]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Something went wrong!
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            We apologize for the inconvenience. Our team has been notified.
          </p>

          {process.env.NODE_ENV === 'development' && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6 text-left">
              <h3 className="text-sm font-medium text-red-800 mb-2">
                Development Error Details:
              </h3>
              <pre className="text-xs text-red-700 whitespace-pre-wrap overflow-auto max-h-32">
                {error?.message || 'Unknown error'}
                {error?.stack ? '\n\nStack:\n' + error.stack : ''}
                {error?.digest ? '\n\nDigest: ' + error.digest : ''}
              </pre>
            </div>
          )}
          <button
            onClick={reset}
            className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition-colors"
          >
            Try again
          </button>
        </div>
      </div>
    </div>
  );
}
