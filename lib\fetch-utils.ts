/**
 * Robust Fetch Utilities with Exponential Backoff and Error Handling
 *
 * Features:
 * - Exponential backoff retry logic (100ms, 500ms, 2s delays)
 * - AbortError handling with user-friendly messages
 * - Request deduplication to prevent multiple simultaneous calls
 * - Progressive timeout handling
 * - Structured error responses
 * - Request ID tracing for debugging
 */

import { useState, useCallback } from 'react';

export interface FetchOptions extends RequestInit {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  retryMultiplier?: number;
  deduplicationKey?: string;
}

export interface ApiError extends Error {
  status?: number;
  code?: string;
  requestId?: string;
  retryable?: boolean;
  timestamp?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  requestId?: string;
  timestamp?: string;
  retryable?: boolean;
}

// Request deduplication cache
const pendingRequests = new Map<string, Promise<Response>>();

// Request ID generation for tracing
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Create structured API error
function createApiError(
  message: string,
  status?: number,
  code?: string,
  requestId?: string,
  retryable = false
): ApiError {
  const error = new Error(message) as ApiError;
  error.status = status;
  error.code = code;
  error.requestId = requestId;
  error.retryable = retryable;
  error.timestamp = new Date().toISOString();
  return error;
}

// Check if error is retryable
function isRetryableError(error: any): boolean {
  // Network errors are retryable
  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    return true;
  }
  
  // Timeout errors are retryable
  if (error.name === 'AbortError' || error.message.includes('timeout')) {
    return true;
  }
  
  // 5xx server errors are retryable
  if (error.status >= 500) {
    return true;
  }
  
  // 408 Request Timeout is retryable
  if (error.status === 408) {
    return true;
  }
  
  // 429 Too Many Requests is retryable
  if (error.status === 429) {
    return true;
  }
  
  return false;
}

// Sleep utility for delays
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Enhanced fetch with exponential backoff and comprehensive error handling
 */
export async function robustFetch<T = any>(
  url: string,
  options: FetchOptions = {}
): Promise<ApiResponse<T>> {
  const {
    timeout = parseInt(process.env.NEXT_PUBLIC_FETCH_TIMEOUT || '10000', 10),
    retries = 3,
    retryDelay = 100,
    retryMultiplier = 5,
    deduplicationKey,
    ...fetchOptions
  } = options;

  const requestId = generateRequestId();
  const startTime = Date.now();
  
  console.log(`🚀 [${requestId}] Starting request to ${url}`);

  // Request deduplication
  const dedupKey = deduplicationKey || `${url}_${JSON.stringify(fetchOptions)}`;
  if (pendingRequests.has(dedupKey)) {
    console.log(`🔄 [${requestId}] Deduplicating request for ${url}`);
    try {
      const response = await pendingRequests.get(dedupKey)!;
      return await response.clone().json();
    } catch (error) {
      // If deduplicated request fails, continue with new request
      pendingRequests.delete(dedupKey);
    }
  }

  let lastError: any;
  
  for (let attempt = 0; attempt <= retries; attempt++) {
    const attemptStartTime = Date.now();
    const controller = new AbortController();
    
    // Set up timeout
    const timeoutId = setTimeout(() => {
      console.warn(`⏰ [${requestId}] Request timeout after ${timeout}ms (attempt ${attempt + 1})`);
      controller.abort();
    }, timeout);

    try {
      console.log(`🔄 [${requestId}] Attempt ${attempt + 1}/${retries + 1}`);
      
      // Create the fetch promise
      const fetchPromise = fetch(url, {
        ...fetchOptions,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': requestId,
          ...fetchOptions.headers,
        },
      });

      // Store in deduplication cache
      if (attempt === 0) {
        pendingRequests.set(dedupKey, fetchPromise);
      }

      const response = await fetchPromise;
      clearTimeout(timeoutId);
      
      const attemptTime = Date.now() - attemptStartTime;
      console.log(`📊 [${requestId}] Response received in ${attemptTime}ms (status: ${response.status})`);

      // Clean up deduplication cache
      pendingRequests.delete(dedupKey);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const error = createApiError(
          errorData.error || `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          errorData.code,
          requestId,
          isRetryableError({ status: response.status })
        );
        
        if (!isRetryableError(error) || attempt === retries) {
          throw error;
        }
        
        lastError = error;
        console.warn(`⚠️ [${requestId}] Retryable error (attempt ${attempt + 1}): ${error.message}`);
      } else {
        const result = await response.json();
        const totalTime = Date.now() - startTime;
        console.log(`✅ [${requestId}] Request completed successfully in ${totalTime}ms`);
        
        // If the API already returns a structured response with success/data, use it directly
        if (result.success !== undefined && result.data !== undefined) {
          return result;
        }

        // Otherwise, wrap the result in our standard structure
        return {
          success: true,
          data: result,
          requestId,
          timestamp: new Date().toISOString()
        };
      }
    } catch (error: any) {
      clearTimeout(timeoutId);
      pendingRequests.delete(dedupKey);
      
      const attemptTime = Date.now() - attemptStartTime;
      console.error(`❌ [${requestId}] Attempt ${attempt + 1} failed after ${attemptTime}ms:`, error);

      // Handle specific error types
      if (error.name === 'AbortError') {
        lastError = createApiError(
          'Request was interrupted. Please check your connection and try again.',
          408,
          'ABORT_ERROR',
          requestId,
          true
        );
      } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
        lastError = createApiError(
          'Network error. Please check your internet connection.',
          0,
          'NETWORK_ERROR',
          requestId,
          true
        );
      } else {
        lastError = error;
      }

      // Don't retry on the last attempt or non-retryable errors
      if (attempt === retries || !isRetryableError(lastError)) {
        break;
      }
    }

    // Exponential backoff delay
    if (attempt < retries) {
      const delay = retryDelay * Math.pow(retryMultiplier, attempt);
      console.log(`⏳ [${requestId}] Waiting ${delay}ms before retry...`);
      await sleep(delay);
    }
  }

  // All attempts failed
  const totalTime = Date.now() - startTime;
  console.error(`🚨 [${requestId}] All attempts failed after ${totalTime}ms`);
  
  return {
    success: false,
    error: lastError?.message || 'Request failed after multiple attempts',
    code: lastError?.code || 'UNKNOWN_ERROR',
    requestId,
    timestamp: new Date().toISOString(),
    retryable: lastError?.retryable || false
  };
}

/**
 * Dashboard-specific fetch hook with progressive loading states
 */
export function useDashboardFetch() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSlowLoading, setIsSlowLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRequestId, setLastRequestId] = useState<string | null>(null);
  const [responseTime, setResponseTime] = useState<number | null>(null);

  const fetchDashboard = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    setIsSlowLoading(false);
    setResponseTime(null);

    // Show slow loading indicator after 2 seconds
    const slowLoadingTimeout = setTimeout(() => {
      setIsSlowLoading(true);
      console.log('⏳ Dashboard request is taking longer than expected...');
    }, 2000);

    try {
      const startTime = Date.now();
      const result = await robustFetch('/api/dashboard', {
        credentials: 'include',
        deduplicationKey: 'dashboard-data'
      });

      clearTimeout(slowLoadingTimeout);
      const endTime = Date.now();
      setResponseTime(endTime - startTime);
      setLastRequestId(result.requestId || null);

      if (!result.success) {
        throw createApiError(
          result.error || 'Failed to load dashboard data',
          undefined,
          result.code,
          result.requestId,
          result.retryable
        );
      }

      return result.data;
    } catch (error: any) {
      clearTimeout(slowLoadingTimeout);
      console.error('Dashboard fetch error:', error);

      // Set user-friendly error messages
      if (error.code === 'ABORT_ERROR') {
        setError('Connection interrupted. Please try again.');
      } else if (error.code === 'NETWORK_ERROR') {
        setError('Network error. Please check your internet connection.');
      } else if (error.status === 401) {
        setError('Session expired. Please log in again.');
      } else if (error.status === 408) {
        setError('Request timed out. Please try again.');
      } else {
        setError(error.message || 'Failed to load dashboard data');
      }

      throw error;
    } finally {
      setIsLoading(false);
      setIsSlowLoading(false);
    }
  }, []);

  const retry = useCallback(() => {
    return fetchDashboard();
  }, [fetchDashboard]);

  return {
    fetchDashboard,
    retry,
    isLoading,
    isSlowLoading,
    error,
    lastRequestId,
    responseTime,
    clearError: () => setError(null)
  };
}


