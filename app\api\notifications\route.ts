import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { createClient } from '@supabase/supabase-js';
import { createSupabaseServerClient } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log('🔔 Notifications API called for user:', user.id);

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const unreadOnly = searchParams.get('unread') === 'true';

    // Initialize Supabase client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    console.log('🔔 Fetching real notifications from Supabase');

    let notifications: any[] = [];

    try {
      // Try to get real notifications from Supabase
      let query = supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (unreadOnly) {
        query = query.eq('read', false);
      }

      const { data, error } = await query;

      if (error) {
        console.warn('Supabase notifications query failed:', error);
        // Fallback to welcome notification for new users
        notifications = [{
          id: 'welcome-1',
          user_id: user.id,
          title: 'Welcome to Slack Summary Scribe!',
          message: 'Start creating AI-powered summaries of your conversations.',
          type: 'welcome',
          read: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: {
            action_url: '/upload',
            icon: '🚀'
          }
        }];
      } else {
        notifications = data || [];
      }

      console.log(`🔔 Found ${notifications.length} notifications for user ${user.id}`);

    } catch (error) {
      console.warn('Error fetching notifications:', error);
      // Fallback to empty array
      notifications = [];
    }

    return NextResponse.json({
      success: true,
      data: notifications || [],
      total: notifications?.length || 0
    });

  } catch (error) {
    console.error('Notifications API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log('🔔 Create Notification API called for user:', user.id);

    const body = await request.json();
    const { type, title, message, data: notificationData, organization_id } = body;

    if (!type || !title || !message) {
      return NextResponse.json(
        { error: 'Missing required fields: type, title, message' },
        { status: 400 }
      );
    }

    const supabase = await createSupabaseServerClient();

    // Create notification in database
    const { data: notification, error } = await supabase
      .from('notifications')
      .insert({
        user_id: user.id,
        organization_id: organization_id,
        type,
        title,
        message,
        metadata: notificationData || {},
        read: false
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating notification:', error);
      return NextResponse.json(
        { error: 'Failed to create notification' },
        { status: 500 }
      );
    }

    console.log('🔔 Notification created:', notification.id);

    return NextResponse.json({
      success: true,
      data: notification
    });

  } catch (error) {
    console.error('Create notification API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log('🔔 Update Notification API called for user:', user.id);

    const body = await request.json();
    const { notificationId, markAllAsRead } = body;
    const supabase = await createSupabaseServerClient();

    if (markAllAsRead) {
      // Mark all notifications as read
      const { error } = await supabase
        .from('notifications')
        .update({ read: true, updated_at: new Date().toISOString() })
        .eq('user_id', user.id)
        .eq('read', false);

      if (error) {
        console.error('Error marking all notifications as read:', error);
        return NextResponse.json(
          { error: 'Failed to mark notifications as read' },
          { status: 500 }
        );
      }

      console.log('🔔 All notifications marked as read for user:', user.id);

      return NextResponse.json({
        success: true,
        message: 'All notifications marked as read'
      });
    } else if (notificationId) {
      // Mark specific notification as read
      const { data: notification, error } = await supabase
        .from('notifications')
        .update({ read: true, updated_at: new Date().toISOString() })
        .eq('id', notificationId)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) {
        console.error('Error marking notification as read:', error);
        return NextResponse.json(
          { error: 'Failed to mark notification as read' },
          { status: 500 }
        );
      }

      console.log('🔔 Notification marked as read:', notificationId);

      return NextResponse.json({
        success: true,
        data: notification,
        message: 'Notification marked as read (Demo Mode)'
      });
    } else {
      return NextResponse.json(
        { error: 'Missing notification ID or markAllAsRead flag' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Update notification API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
