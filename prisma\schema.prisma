// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  subscriptions Subscription[]
  slackTokens   SlackToken[]
  summaries     Summary[]

  @@map("users")
}

model Subscription {
  id                String            @id @default(cuid())
  userId            String
  plan              SubscriptionPlan
  status            SubscriptionStatus
  cashfreeOrderId   String?           @unique
  cashfreePaymentId String?           @unique
  currentPeriodStart DateTime?
  currentPeriodEnd   DateTime?
  cancelAtPeriodEnd Boolean           @default(false)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model SlackToken {
  id           String   @id @default(cuid())
  userId       String
  teamId       String
  teamName     String
  accessToken  String
  botUserId    String?
  scope        String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, teamId])
  @@map("slack_tokens")
}

model Summary {
  id          String   @id @default(cuid())
  userId      String
  teamId      String
  channelId   String
  channelName String
  title       String
  content     String   @db.Text
  messageCount Int     @default(0)
  participants String[] @default([])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("summaries")
}

enum SubscriptionPlan {
  FREE
  PRO
  ENTERPRISE
}

enum SubscriptionStatus {
  ACTIVE
  CANCELED
  PAST_DUE
  INCOMPLETE
  INCOMPLETE_EXPIRED
  TRIALING
  UNPAID
}
