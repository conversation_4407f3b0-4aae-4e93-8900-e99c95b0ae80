import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { getCurrentUser } from '@/lib/user-management';
import { uploadStatusTracker } from '@/lib/upload-status-tracker';

export async function GET(request: NextRequest) {
  try {
    console.log('📁 Upload status API called - Public Live Mode');

    // Get user (anonymous mode supported)
    const user = await getCurrentUser();
    const userId = user?.id || 'anonymous-user';

    const { searchParams } = new URL(request.url);
    const fileId = searchParams.get('fileId');

    if (!fileId) {
      return NextResponse.json(
        { success: false, error: 'File ID is required' },
        { status: 400 }
      );
    }

    console.log('📁 Checking status for file:', fileId);

    // First check in-memory tracker for real-time status
    const trackedStatus = uploadStatusTracker.getStatus(fileId);
    if (trackedStatus) {
      console.log('📁 Found tracked status:', trackedStatus.status);
      return NextResponse.json({
        success: true,
        data: {
          status: trackedStatus.status,
          progress: trackedStatus.progress,
          summaryId: trackedStatus.summaryId,
          errorMessage: trackedStatus.errorMessage,
          fileName: trackedStatus.fileName,
          fileSize: trackedStatus.fileSize,
          processingSteps: trackedStatus.processingSteps,
          lastUpdate: trackedStatus.lastUpdate
        }
      });
    }

    // Fallback to database check
    const supabase = await createSupabaseServerClient();
    let uploadRecord = null;
    let uploadError = null;

    try {
      // For anonymous users, search by fileId only (public mode)
      let query = supabase
        .from('file_uploads')
        .select('*')
        .eq('id', fileId);

      // Only filter by user_id if not anonymous
      if (userId !== 'anonymous-user') {
        query = query.eq('user_id', userId);
      }

      const result = await query.single();
      uploadRecord = result.data;
      uploadError = result.error;
    } catch (error) {
      console.warn('File uploads table may not exist yet:', error);
      // Return a default processing status for public mode
      return NextResponse.json({
        success: true,
        data: {
          status: 'processing',
          summaryId: null,
          errorMessage: null,
          fileName: `file-${fileId}`,
          progress: 75
        }
      });
    }

    if (uploadError) {
      console.error('Error fetching upload record:', uploadError);
      if (uploadError.code === 'PGRST116') {
        // Table doesn't exist, return default status
        return NextResponse.json({
          success: true,
          data: {
            status: 'completed',
            summaryId: null,
            errorMessage: null
          }
        });
      }
      return NextResponse.json(
        { success: false, error: 'Upload record not found' },
        { status: 404 }
      );
    }

    console.log('📁 Upload status:', uploadRecord.status);

    // Extract data from processing_result
    const processingResult = uploadRecord.processing_result || {};
    const summaryId = processingResult.summary_id;
    const errorMessage = processingResult.error;

    return NextResponse.json({
      success: true,
      data: {
        status: uploadRecord.upload_status,
        summaryId: summaryId,
        errorMessage: errorMessage,
        processedAt: uploadRecord.updated_at,
        fileName: uploadRecord.filename,
        fileSize: uploadRecord.file_size
      }
    });

  } catch (error) {
    console.error('Status API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
