/**
 * Unified Payment Checkout API Route
 * Handles both Stripe and Cashfree with automatic fallback
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { createCheckoutSession, getPreferredGateway } from '@/lib/dual-payment-gateway';
import { withSubscriptionCheck } from '@/lib/subscription-middleware';
// Removed: import { SentryTracker } from '@/lib/sentry.client';

/**
 * POST /api/payments/checkout
 * Create checkout session with automatic gateway selection
 */
export async function POST(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const body = await req.json();
      const { 
        plan_id, 
        success_url, 
        cancel_url, 
        region,
        metadata = {} 
      } = body;

      if (!plan_id) {
        return NextResponse.json(
          { error: 'Plan ID is required' },
          { status: 400 }
        );
      }

      // Get plan pricing
      const planPricing = getPlanPricing(plan_id);
      if (!planPricing) {
        return NextResponse.json(
          { error: 'Invalid plan ID' },
          { status: 400 }
        );
      }

      // Get user's region from headers or IP
      const userRegion = region || getUserRegion(req);
      const preferredGateway = getPreferredGateway(userRegion);

      console.log(`Creating checkout for plan ${plan_id} in region ${userRegion} using ${preferredGateway.name}`);

      // Create checkout session
      const result = await createCheckoutSession({
        amount: planPricing.amount,
        currency: planPricing.currency,
        customer_email: user.email,
        customer_id: context.userId,
        plan_id: planPricing.gateway_plan_id[preferredGateway.id] || plan_id,
        success_url: success_url || `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?payment=success`,
        cancel_url: cancel_url || `${process.env.NEXT_PUBLIC_APP_URL}/pricing?payment=cancelled`,
        metadata: {
          organization_id: user.id,
          current_tier: context.subscription.tier,
          ...metadata,
        },
      }, userRegion);

      if (!result.success) {
        return NextResponse.json(
          { 
            error: result.error,
            gateway_attempted: result.gateway_used,
            fallback_attempted: result.fallback_attempted,
          },
          { status: 400 }
        );
      }

      return NextResponse.json({
        success: true,
        checkout_url: result.checkout_url,
        session_id: result.session_id,
        payment_id: result.payment_id,
        gateway_used: result.gateway_used,
        fallback_attempted: result.fallback_attempted,
      });

    } catch (error) {
      console.error('Payment checkout error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to create checkout session' },
        { status: 500 }
      );
    }
  });
}

/**
 * Get plan pricing configuration
 */
function getPlanPricing(planId: string) {
  const plans = {
    'pro': {
      amount: 2900, // $29.00 in cents
      currency: 'USD',
      gateway_plan_id: {
        stripe: process.env.STRIPE_PRO_PRICE_ID || 'price_pro',
        cashfree: 'pro_plan',
        demo: 'demo_pro_plan',
      },
    },
    'enterprise': {
      amount: 9900, // $99.00 in cents
      currency: 'USD',
      gateway_plan_id: {
        stripe: process.env.STRIPE_ENTERPRISE_PRICE_ID || 'price_enterprise',
        cashfree: 'enterprise_plan',
        demo: 'demo_enterprise_plan',
      },
    },
  };

  return plans[planId as keyof typeof plans];
}

/**
 * Get user's region from request
 */
function getUserRegion(request: NextRequest): string {
  // Try to get region from Cloudflare header
  const cfCountry = request.headers.get('cf-ipcountry');
  if (cfCountry) {
    return cfCountry;
  }

  // Try to get region from Vercel header
  const vercelCountry = request.headers.get('x-vercel-ip-country');
  if (vercelCountry) {
    return vercelCountry;
  }

  // Try to get from custom header
  const customRegion = request.headers.get('x-user-region');
  if (customRegion) {
    return customRegion;
  }

  // Default to US
  return 'US';
}
