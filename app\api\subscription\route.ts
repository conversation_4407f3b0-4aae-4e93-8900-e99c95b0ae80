/**
 * Subscription Management API Route
 * Handles subscription CRUD operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import {
  getUserSubscription,
  createCheckoutSession,
  incrementUsage,
} from '@/lib/subscription-service';
// Removed: import { SentryTracker } from '@/lib/sentry.client';

// Demo mode pricing plans
const DEMO_PRICING_PLANS = {
  FREE: {
    name: 'Free',
    price: 0,
    currency: 'usd',
    interval: 'month',
    features: [
      '10 summaries per month',
      'DeepSeek R1 AI model',
      'Basic Slack integration',
      'Email support',
      'Standard templates'
    ],
    limits: {
      monthlySummaries: 10,
      teamMembers: 1,
      aiModels: ['deepseek-r1'],
      crmIntegrations: 0,
      scheduledPosts: 0,
      analytics: false,
      auditLogs: false,
      ssoIntegration: false
    },
  },
  PRO: {
    name: 'Pro',
    price: 29,
    currency: 'usd',
    interval: 'month',
    features: [
      '100 summaries per month',
      'All AI models (GPT-4o-mini, Claude)',
      'Advanced Slack integration',
      'Priority support',
      'Custom templates',
      'Basic analytics'
    ],
    limits: {
      monthlySummaries: 100,
      teamMembers: 5,
      aiModels: ['deepseek-r1', 'gpt-4o-mini', 'claude-3-haiku'],
      crmIntegrations: 2,
      scheduledPosts: 10,
      analytics: true,
      auditLogs: false,
      ssoIntegration: false
    },
  },
  ENTERPRISE: {
    name: 'Enterprise',
    price: 99,
    currency: 'usd',
    interval: 'month',
    features: [
      'Unlimited summaries',
      'All AI models + premium',
      'Full Slack automation',
      '24/7 support',
      'Custom integrations',
      'Advanced analytics',
      'Audit logs',
      'SSO integration'
    ],
    limits: {
      monthlySummaries: -1, // Unlimited
      teamMembers: -1, // Unlimited
      aiModels: ['deepseek-r1', 'gpt-4o-mini', 'claude-3-haiku', 'gpt-4o', 'claude-3-opus'],
      crmIntegrations: -1, // Unlimited
      scheduledPosts: -1, // Unlimited
      analytics: true,
      auditLogs: true,
      ssoIntegration: true
    },
  }
};

/**
 * GET /api/subscription
 * Get current user's subscription details
 */
export async function GET() {
  try {
    // Get authenticated user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const userId = user.id;
    

    const subscription = await getUserSubscription(userId);
    
    if (!subscription) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 }
      );
    }

    const plan = DEMO_PRICING_PLANS[subscription.subscription_tier];

    return NextResponse.json({
      success: true,
      subscription: {
        id: subscription.id,
        tier: subscription.subscription_tier,
        status: subscription.status,
        cancel_at_period_end: subscription.cancel_at_period_end,
        monthly_summary_limit: subscription.monthly_summary_limit,
        monthly_summaries_used: subscription.monthly_summaries_used,
        created_at: subscription.created_at,
        updated_at: subscription.updated_at,
      },
      plan: {
        name: plan.name,
        price: plan.price,
        currency: plan.currency,
        interval: plan.interval,
        features: plan.features,
        limits: plan.limits,
      },
      usage: {
        summaries_used: subscription.monthly_summaries_used,
        summaries_limit: subscription.monthly_summary_limit,
        usage_percentage: subscription.monthly_summary_limit === -1
          ? 0
          : Math.round((subscription.monthly_summaries_used / subscription.monthly_summary_limit) * 100),
        can_create_summary: subscription.monthly_summary_limit === -1
          ? true
          : subscription.monthly_summaries_used < subscription.monthly_summary_limit,
      }
    });

  } catch (error) {
    console.error('Error fetching subscription:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to fetch subscription' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/subscription
 * Manage subscription actions (cancel, reactivate, etc.)
 */
export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const userId = user.id;
    

    const { action } = await request.json();

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'cancel':
        // Demo mode - simulate cancellation
        console.log(`🎭 Demo mode: Simulating subscription cancellation for user ${userId}`);
        return NextResponse.json({
          success: true,
          message: 'Demo mode: Subscription cancellation simulated'
        });

      case 'reactivate':
        // Demo mode - simulate reactivation
        console.log(`🎭 Demo mode: Simulating subscription reactivation for user ${userId}`);
        return NextResponse.json({
          success: true,
          message: 'Demo mode: Subscription reactivation simulated'
        });

      case 'increment_usage':
        const { amount = 1 } = await request.json();
        const canIncrement = await incrementUsage(userId, amount);

        if (!canIncrement) {
          return NextResponse.json(
            { error: 'Usage limit exceeded' },
            { status: 403 }
          );
        }

        return NextResponse.json({
          success: true,
          message: 'Usage incremented successfully'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error managing subscription:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to manage subscription' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/subscription
 * Update subscription preferences
 */
export async function PUT(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const userId = user.id;
    

    // For now, this endpoint is reserved for future subscription preference updates
    // such as billing address, payment method preferences, etc.
    
    return NextResponse.json({
      success: true,
      message: 'Subscription preferences updated'
    });

  } catch (error) {
    console.error('Error updating subscription:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to update subscription' },
      { status: 500 }
    );
  }
}
