{"name": "slack-summary-scribe", "private": true, "version": "0.0.0", "scripts": {"dev": "next dev --port 3001", "dev:enhanced": "tsx scripts/dev-start.ts", "dev:diagnostics": "tsx scripts/dev-diagnostics.ts", "dev:clean": "npm run clean && npm run dev", "dev:validate": "npm run validate:env && npm run dev", "dev:safe": "npm run dev:diagnostics", "dev:test": "tsx scripts/validate-dev-server.ts", "dev:full": "npm run dev:validate", "dev:https": "npm run dev & npx localtunnel --port 3000 --subdomain slack-summary-dev", "dev:ngrok": "npm run dev & ngrok http 3000", "setup:https": "npm install -g localtunnel && echo 'HTTPS dev setup complete'", "validate:auth": "node scripts/validate-auth-setup.js", "test:separation": "node scripts/test-server-client-separation.js", "build": "npm run check-env && next build", "build:clean": "powershell -ExecutionPolicy Bypass -File scripts/clean-build.ps1", "start": "next start", "clean": "rimraf .next node_modules/.cache .turbo dist && echo 'Build artifacts cleaned'", "check-env": "tsx scripts/check-env.ts", "validate-env": "node scripts/validate-production-env.ts", "validate-production": "tsx scripts/production-validation.ts", "prebuild-disabled": "npm run clean", "lint": "eslint .", "lint:fix": "eslint . --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "seed-test-user": "npx tsx scripts/seed-test-user.ts", "test:e2e": "playwright test", "test:auth": "playwright test tests/e2e/auth-system.spec.ts", "test:flow": "tsx scripts/test-complete-flow.ts", "validate:env": "tsx scripts/validate-env.ts", "test:auth-protection": "tsx scripts/test-auth-protection.ts", "clean:restart": "tsx scripts/clean-restart.ts", "validate:deployment": "node scripts/validate-vercel-deployment.js", "deploy:validate": "npm run build && npm run validate:deployment", "test:load": "artillery run tests/load/basic-load-test.yml", "test:stress": "artillery run tests/load/stress-test.yml", "test:load:report": "artillery run tests/load/basic-load-test.yml --output load-test-report.json", "test:performance": "npm run test:load && npm run test:stress", "test:a11y": "node scripts/accessibility-audit.js", "lighthouse": "node scripts/lighthouse-audit.js", "seed-demo": "ts-node scripts/seed-demo-data.ts", "analyze": "cross-env ANALYZE=true next build", "db:types": "supabase gen types typescript --project-id YOUR_PROJECT_ID > lib/database.types.ts", "deploy": "bash scripts/deploy-production.sh", "deploy:prod": "bash scripts/deploy-production.sh --production", "production:build": "NODE_ENV=production npm run build", "production:validate": "npm run validate-env && npm run production:build", "db:reset": "supabase db reset", "db:seed": "npm run seed-demo", "clean-install": "npm run clean && rimraf node_modules package-lock.json && npm install", "health-check": "npm run type-check && npm run lint && npm run build", "deploy-production": "tsx scripts/deploy-production.ts", "setup-monitoring": "tsx scripts/setup-monitoring.ts", "setup-growth": "tsx scripts/setup-growth-automation.ts", "prepare-launch": "tsx scripts/product-hunt-launch.ts", "e2e-launch-validation": "tsx scripts/e2e-launch-validation.ts", "validate-deployment": "tsx scripts/deployment-validation.ts", "test-onboarding": "tsx scripts/e2e-onboarding-test.ts", "generate-launch-assets": "tsx scripts/product-hunt-generator.ts", "launch-readiness": "tsx scripts/launch-readiness.ts", "launch-production": "node scripts/launch-production.js", "postinstall": "npx prisma generate", "postbuild": "echo 'Sitemap generation disabled for testing'"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hookform/resolvers": "^3.9.0", "@next-auth/supabase-adapter": "^0.2.1", "@notionhq/client": "^4.0.1", "@prisma/client": "^6.11.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@sendgrid/mail": "^8.1.5", "@sentry/nextjs": "^9.40.0", "@sentry/react": "^9.40.0", "@slack/events-api": "^3.0.1", "@slack/oauth": "^3.0.3", "@slack/web-api": "^7.9.3", "@stripe/stripe-js": "^7.5.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.10", "@swc/helpers": "^0.5.17", "@tanstack/react-query": "^5.56.2", "@types/mixpanel-browser": "^2.60.0", "@types/react-dropzone": "^4.2.2", "@types/web-push": "^3.6.4", "cashfree-pg": "^5.0.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "compression": "^1.8.0", "cors": "^2.8.5", "d3-scale": "^4.0.2", "d3-shape": "^3.2.0", "date-fns": "^3.6.0", "detect-node-es": "^1.1.0", "dotenv": "^16.6.1", "embla-carousel-react": "^8.3.0", "exceljs": "^4.4.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "framer-motion": "^12.19.1", "glob": "^11.0.3", "googleapis": "^152.0.0", "helmet": "^8.1.0", "input-otp": "^1.2.4", "is-number": "^7.0.0", "json2csv": "^6.0.0-alpha.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.462.0", "mammoth": "^1.9.1", "mixpanel-browser": "^2.65.0", "morgan": "^1.10.0", "next": "^15.3.4", "next-auth": "^4.24.11", "next-sitemap": "^4.2.3", "next-themes": "^0.3.0", "object-inspect": "^1.13.4", "openai": "^5.7.0", "pdf-parse": "^1.1.1", "pdfkit": "^0.17.1", "posthog-js": "^1.257.0", "posthog-node": "^5.5.1", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.5.2", "react-resizable-panels": "^2.1.3", "recharts": "^2.15.4", "resend": "^4.7.0", "sonner": "^1.5.0", "stripe": "^18.3.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "web-push": "^3.6.7", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@playwright/test": "^1.54.0", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/compression": "^1.8.1", "@types/cors": "^2.8.17", "@types/cross-spawn": "^6.0.6", "@types/express": "^4.17.21", "@types/express-rate-limit": "^5.1.3", "@types/jest": "^30.0.0", "@types/json2csv": "^5.0.7", "@types/morgan": "^1.9.10", "@types/node": "22.16.5", "@types/pdf-parse": "^1.1.5", "@types/pdfkit": "^0.14.0", "@types/react": "18.3.23", "@types/react-dom": "^18.3.0", "@types/supertest": "^6.0.3", "artillery": "^2.0.23", "autoprefixer": "^10.4.20", "cross-spawn": "^7.0.6", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "lovable-tagger": "^1.1.7", "node-mocks-http": "^1.17.2", "null-loader": "^4.0.1", "postcss": "^8.4.47", "prisma": "^6.11.1", "rimraf": "^6.0.1", "string-replace-loader": "^3.2.0", "supertest": "^7.1.1", "tailwindcss": "^3.4.11", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "5.8.3", "typescript-eslint": "^8.0.1"}}