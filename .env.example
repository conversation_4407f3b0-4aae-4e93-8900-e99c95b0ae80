# =============================================================================
# SLACK SUMMARY SCRIBE - ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env.local and fill in your actual values
# Never commit .env.local to version control!

# -----------------------------------------------------------------------------
# REQUIRED FOR VERCEL DEPLOYMENT
# -----------------------------------------------------------------------------
# These are CRITICAL for Vercel deployment - ensure all are set

# Supabase Database (REQUIRED)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Database URL for Prisma (REQUIRED)
DATABASE_URL="postgresql://postgres:[password]@db.[project].supabase.co:5432/postgres"

# Slack OAuth (REQUIRED)
NEXT_PUBLIC_SLACK_CLIENT_ID=your_slack_client_id
SLACK_CLIENT_SECRET=your_slack_client_secret
SLACK_SIGNING_SECRET=your_slack_signing_secret

# AI Service (REQUIRED)
OPENROUTER_API_KEY=your_openrouter_api_key

# Premium AI Models (OPTIONAL - for Pro/Enterprise plans)
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# Cashfree Payments (REQUIRED for SaaS)
CASHFREE_APP_ID=your_cashfree_app_id
CASHFREE_SECRET_KEY=your_cashfree_secret_key
CASHFREE_ENVIRONMENT=sandbox

# Site URL (REQUIRED for production)
NEXT_PUBLIC_SITE_URL=https://your-app.vercel.app

# JWT Secret (REQUIRED)
JWT_SECRET=your_jwt_secret_minimum_32_characters

# -----------------------------------------------------------------------------
# OPTIONAL CONFIGURATION
# -----------------------------------------------------------------------------

# Google OAuth (Optional)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Email Service (Optional)
RESEND_API_KEY=your_resend_api_key
EMAIL_FROM=<EMAIL>
SUPPORT_EMAIL=<EMAIL>

# Slack Webhook (Optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL

# Legacy DeepSeek API (deprecated - use OpenRouter instead)
DEEPSEEK_API_KEY=your_deepseek_api_key


# -----------------------------------------------------------------------------
# ADVANCED OPTIONAL CONFIGURATION
# -----------------------------------------------------------------------------

# Clerk Authentication (Alternative to Supabase Auth)
CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key

# Stripe Payments (Alternative to Cashfree)
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# NextAuth (Alternative Auth)
NEXTAUTH_SECRET=your_nextauth_secret_key_here
NEXTAUTH_URL=http://localhost:3000

# Security
ENCRYPTION_KEY=your_encryption_key_32_characters

# Vercel Deployment (for GitHub Actions)
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_vercel_org_id
VERCEL_PROJECT_ID=your_vercel_project_id

# Notion Integration
NOTION_API_TOKEN=your_notion_api_token
NOTION_DATABASE_ID=your_notion_database_id

# CRM Integrations (OPTIONAL)
HUBSPOT_CLIENT_ID=your_hubspot_client_id
HUBSPOT_CLIENT_SECRET=your_hubspot_client_secret
HUBSPOT_REDIRECT_URI=https://your-app.vercel.app/api/crm/hubspot/callback
SALESFORCE_CLIENT_ID=your_salesforce_client_id
SALESFORCE_CLIENT_SECRET=your_salesforce_client_secret
SALESFORCE_REDIRECT_URI=https://your-app.vercel.app/api/crm/salesforce/callback
SALESFORCE_SANDBOX_URL=https://test.salesforce.com

# Encryption for CRM tokens (REQUIRED for CRM features)
ENCRYPTION_KEY=your_32_character_encryption_key_here

# Stripe Payments (Fallback to Cashfree)
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
STRIPE_PRICE_ID_PRO=price_your_pro_price_id
STRIPE_PRICE_ID_ENTERPRISE=price_your_enterprise_price_id

# Notion Integration (OPTIONAL)
NOTION_CLIENT_ID=your_notion_client_id
NOTION_CLIENT_SECRET=your_notion_client_secret
NOTION_REDIRECT_URI=https://your-app.vercel.app/api/notion/callback

# Monitoring & Error Tracking
SENTRY_AUTH_TOKEN=your_sentry_auth_token
SENTRY_ORG=your_sentry_org
SENTRY_PROJECT=your_sentry_project
SENTRY_DSN=your_sentry_dsn