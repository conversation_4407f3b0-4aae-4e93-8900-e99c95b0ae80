import { NextRequest, NextResponse } from 'next/server'

/**
 * Test endpoint to verify cookie setting functionality
 */
export async function GET(request: NextRequest) {
  const response = NextResponse.json({
    message: 'Cookie test endpoint',
    timestamp: new Date().toISOString(),
  })

  // Test setting cookies manually (similar to OAuth callback)
  const isSecure = request.nextUrl.protocol === 'https:'
  
  response.cookies.set('test-auth-token', 'test-token-value-' + Date.now(), {
    httpOnly: false,
    secure: isSecure,
    sameSite: 'lax',
    path: '/',
    maxAge: 60 * 60, // 1 hour
    domain: request.nextUrl.hostname === 'localhost' ? undefined : request.nextUrl.hostname,
  })

  response.cookies.set('test-refresh-token', 'test-refresh-value-' + Date.now(), {
    httpOnly: false,
    secure: isSecure,
    sameSite: 'lax',
    path: '/',
    maxAge: 60 * 60 * 24, // 24 hours
    domain: request.nextUrl.hostname === 'localhost' ? undefined : request.nextUrl.hostname,
  })

  console.log('🧪 Test cookies set:', {
    secure: isSecure,
    domain: request.nextUrl.hostname === 'localhost' ? 'undefined (localhost)' : request.nextUrl.hostname,
    timestamp: new Date().toISOString()
  })

  return response
}

export async function POST(request: NextRequest) {
  // Read current cookies
  const allCookies = request.cookies.getAll()
  const testCookies = allCookies.filter(cookie => 
    cookie.name.startsWith('test-') || 
    cookie.name.startsWith('sb-')
  )

  return NextResponse.json({
    message: 'Cookie verification',
    allCookiesCount: allCookies.length,
    testCookies: testCookies.map(c => ({ 
      name: c.name, 
      hasValue: !!c.value,
      valueLength: c.value?.length || 0
    })),
    allCookieNames: allCookies.map(c => c.name),
    timestamp: new Date().toISOString(),
  })
}
