import type { Metadata } from 'next';

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'https://slack-summary-scribe.vercel.app'),
  title: {
    default: 'Slack Summary Scribe - AI-Powered Summarization Platform (Public Live)',
    template: '%s | Slack Summary Scribe'
  },
  description: 'Transform your Slack conversations, documents, and meeting transcripts into actionable AI summaries. Upload files, generate insights, and export to PDF, Excel, or Notion. Public access - no signup required. Try it now!',
  keywords: [
    'slack summaries',
    'ai conversation analysis',
    'team communication tools',
    'slack integration',
    'meeting summaries',
    'productivity software',
    'artificial intelligence',
    'workplace automation',
    'document summarization',
    'file upload ai',
    'pdf summarization',
    'export summaries',
    'business intelligence',
    'team productivity'
  ],
  authors: [{ name: 'Slack Summary Scribe Team', url: 'https://slacksummaryscribe.com' }],
  creator: 'Slack Summary Scribe',
  publisher: 'Slack Summary Scribe',
  category: 'Productivity Software',
  openGraph: {
    title: 'Slack Summary Scribe - AI-Powered Summarization Platform (Public Live)',
    description: 'Transform your Slack conversations, documents, and meeting transcripts into actionable AI summaries. Public access - no signup required!',
    url: process.env.NEXT_PUBLIC_APP_URL || 'https://slack-summary-scribe.vercel.app',
    siteName: 'Slack Summary Scribe',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Slack Summary Scribe - AI-Powered Conversation Summaries',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Slack Summary Scribe - AI-Powered Conversation Summaries (Public Live)',
    description: 'Transform your Slack conversations into actionable insights with AI-powered summaries. Try it now - no signup required!',
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
};
