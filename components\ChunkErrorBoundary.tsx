'use client'

import React, { Component, ReactNode } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: any
}

export class ChunkErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: any) {
    // Log the error for debugging
    console.error('ChunkErrorBoundary caught an error:', error, errorInfo)

    this.setState({
      error,
      errorInfo
    })

    // Enhanced error reporting (no external dependencies)
    if (typeof window !== 'undefined') {
      console.error('🔥 ChunkErrorBoundary error logged:', {
        message: error.message,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString()
      });
    }

    // Check if it's a chunk loading error
    if (this.isChunkLoadError(error)) {
      this.handleChunkLoadError()
    }
  }

  private isChunkLoadError(error: Error): boolean {
    const chunkErrorPatterns = [
      'ChunkLoadError',
      'Loading chunk',
      'Failed to import',
      'Cannot read properties of undefined (reading \'call\')',
      'runtime.js',
      'Script error'
    ]

    const errorMessage = error.message || ''
    const errorStack = error.stack || ''
    
    return chunkErrorPatterns.some(pattern => 
      errorMessage.includes(pattern) || errorStack.includes(pattern)
    )
  }

  private handleChunkLoadError() {
    // Clear caches and reload
    if (typeof window !== 'undefined') {
      // Clear service worker cache
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations().then(registrations => {
          registrations.forEach(registration => registration.unregister())
        })
      }

      // Clear browser caches
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => caches.delete(name))
        })
      }

      // Reload after a short delay
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    }
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  private handleReload = () => {
    if (typeof window !== 'undefined') {
      window.location.reload()
    }
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default error UI
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full mx-4">
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>
                <div className="space-y-2">
                  <p className="font-medium">Something went wrong</p>
                  <p className="text-sm">
                    {this.isChunkLoadError(this.state.error!) 
                      ? "We're experiencing loading issues. The page will reload automatically."
                      : "An unexpected error occurred. Please try refreshing the page."
                    }
                  </p>
                </div>
              </AlertDescription>
            </Alert>
            
            <div className="flex gap-2">
              <Button onClick={this.handleRetry} variant="outline" className="flex-1">
                Try Again
              </Button>
              <Button onClick={this.handleReload} className="flex-1">
                Reload Page
              </Button>
            </div>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-4 p-4 bg-gray-100 rounded text-xs">
                <summary className="cursor-pointer font-medium">Error Details</summary>
                <pre className="mt-2 whitespace-pre-wrap">
                  {this.state.error.message}
                  {this.state.error.stack}
                </pre>
              </details>
            )}
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default ChunkErrorBoundary
