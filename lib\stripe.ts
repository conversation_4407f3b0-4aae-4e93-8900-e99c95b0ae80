/**
 * Stripe Configuration
 * 
 * Server-side Stripe client and utilities for subscription management
 */

import Strip<PERSON> from 'stripe';
import { STRIPE_CONFIG } from './pricing';

// Initialize Stripe client
export const stripe = new Stripe(STRIPE_CONFIG.secretKey, {
  apiVersion: '2025-06-30.basil',
  typescript: true,
});

// Stripe client-side configuration
export const stripeConfig = {
  publishableKey: STRIPE_CONFIG.publishableKey,
};

// Create checkout session
export async function createCheckoutSession({
  priceId,
  customerId,
  successUrl,
  cancelUrl,
  metadata = {},
}: {
  priceId: string;
  customerId?: string;
  successUrl?: string;
  cancelUrl?: string;
  metadata?: Record<string, string>;
}) {
  try {
    const session = await stripe.checkout.sessions.create({
      mode: 'subscription',
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      customer: customerId,
      success_url: successUrl || STRIPE_CONFIG.successUrl,
      cancel_url: cancelUrl || STRIPE_CONFIG.cancelUrl,
      metadata,
      allow_promotion_codes: true,
      billing_address_collection: 'required',
      subscription_data: {
        metadata,
      },
    });

    return { session, error: null };
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return { 
      session: null, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Create customer portal session
export async function createPortalSession({
  customerId,
  returnUrl,
}: {
  customerId: string;
  returnUrl?: string;
}) {
  try {
    const session = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl || STRIPE_CONFIG.portalUrl,
    });

    return { session, error: null };
  } catch (error) {
    console.error('Error creating portal session:', error);
    return { 
      session: null, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Create or retrieve customer
export async function createOrRetrieveCustomer({
  email,
  userId,
  name,
}: {
  email: string;
  userId: string;
  name?: string;
}) {
  try {
    // First, try to find existing customer by email
    const existingCustomers = await stripe.customers.list({
      email,
      limit: 1,
    });

    if (existingCustomers.data.length > 0) {
      const customer = existingCustomers.data[0];
      
      // Update metadata if needed
      if (!customer.metadata.userId) {
        await stripe.customers.update(customer.id, {
          metadata: { userId },
        });
      }
      
      return { customer, error: null };
    }

    // Create new customer
    const customer = await stripe.customers.create({
      email,
      name,
      metadata: { userId },
    });

    return { customer, error: null };
  } catch (error) {
    console.error('Error creating/retrieving customer:', error);
    return { 
      customer: null, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Get subscription details
export async function getSubscription(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
      expand: ['default_payment_method', 'customer'],
    });

    return { subscription, error: null };
  } catch (error) {
    console.error('Error retrieving subscription:', error);
    return { 
      subscription: null, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Cancel subscription
export async function cancelSubscription(subscriptionId: string, cancelAtPeriodEnd = true) {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: cancelAtPeriodEnd,
    });

    return { subscription, error: null };
  } catch (error) {
    console.error('Error canceling subscription:', error);
    return { 
      subscription: null, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Resume subscription
export async function resumeSubscription(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: false,
    });

    return { subscription, error: null };
  } catch (error) {
    console.error('Error resuming subscription:', error);
    return { 
      subscription: null, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Update subscription
export async function updateSubscription({
  subscriptionId,
  newPriceId,
}: {
  subscriptionId: string;
  newPriceId: string;
}) {
  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    
    const updatedSubscription = await stripe.subscriptions.update(subscriptionId, {
      items: [
        {
          id: subscription.items.data[0].id,
          price: newPriceId,
        },
      ],
      proration_behavior: 'create_prorations',
    });

    return { subscription: updatedSubscription, error: null };
  } catch (error) {
    console.error('Error updating subscription:', error);
    return { 
      subscription: null, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Get customer subscriptions
export async function getCustomerSubscriptions(customerId: string) {
  try {
    const subscriptions = await stripe.subscriptions.list({
      customer: customerId,
      status: 'all',
      expand: ['data.default_payment_method'],
    });

    return { subscriptions: subscriptions.data, error: null };
  } catch (error) {
    console.error('Error retrieving customer subscriptions:', error);
    return { 
      subscriptions: null, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Webhook event verification
export function verifyWebhookSignature(
  payload: string | Buffer,
  signature: string,
  secret: string
) {
  try {
    const event = stripe.webhooks.constructEvent(payload, signature, secret);
    return { event, error: null };
  } catch (error) {
    console.error('Webhook signature verification failed:', error);
    return { 
      event: null, 
      error: error instanceof Error ? error.message : 'Invalid signature' 
    };
  }
}

// Usage-based billing helpers (placeholder for future implementation)
export async function createUsageRecord({
  subscriptionItemId,
  quantity,
  timestamp,
}: {
  subscriptionItemId: string;
  quantity: number;
  timestamp?: number;
}) {
  // TODO: Implement usage-based billing when needed
  console.log('Usage record creation not yet implemented');
  return {
    usageRecord: null,
    error: 'Usage-based billing not implemented'
  };
}

// Get invoice preview for subscription changes (placeholder)
export async function getInvoicePreview({
  customerId,
  subscriptionId,
  newPriceId,
}: {
  customerId: string;
  subscriptionId?: string;
  newPriceId?: string;
}) {
  // TODO: Implement invoice preview when needed
  console.log('Invoice preview not yet implemented');
  return {
    invoice: null,
    error: 'Invoice preview not implemented'
  };
}

// Utility functions
export function formatStripeAmount(amount: number, currency = 'usd'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase(),
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount / 100);
}

export function isSubscriptionActive(status: string): boolean {
  return ['active', 'trialing'].includes(status);
}

export function isSubscriptionPastDue(status: string): boolean {
  return status === 'past_due';
}

export function isSubscriptionCanceled(status: string): boolean {
  return ['canceled', 'incomplete_expired', 'unpaid'].includes(status);
}
