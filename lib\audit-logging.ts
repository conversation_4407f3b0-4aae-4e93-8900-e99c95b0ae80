/**
 * Audit Logging Service
 * Comprehensive audit trail for enterprise compliance
 */

import { createSupabaseServerClient } from '@/lib/supabase-server';
import { SentryTracker } from '@/lib/sentry.client';

export interface AuditLog {
  id: string;
  organization_id: string;
  user_id: string;
  user_email: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  details: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  session_id?: string;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  status: 'success' | 'failure' | 'warning';
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface AuditFilters {
  organization_id?: string;
  user_id?: string;
  action?: string;
  resource_type?: string;
  risk_level?: 'low' | 'medium' | 'high' | 'critical';
  status?: 'success' | 'failure' | 'warning';
  start_date?: string;
  end_date?: string;
  limit?: number;
  offset?: number;
}

export interface ComplianceReport {
  organization_id: string;
  report_period: { start: string; end: string };
  total_events: number;
  events_by_risk_level: Record<string, number>;
  events_by_action: Record<string, number>;
  events_by_user: Array<{ user_id: string; user_email: string; event_count: number }>;
  security_incidents: AuditLog[];
  compliance_score: number;
  recommendations: string[];
  generated_at: string;
}

// Action categories for audit logging
export const AUDIT_ACTIONS = {
  // Authentication & Authorization
  USER_LOGIN: 'user.login',
  USER_LOGOUT: 'user.logout',
  USER_LOGIN_FAILED: 'user.login_failed',
  PASSWORD_CHANGED: 'user.password_changed',
  MFA_ENABLED: 'user.mfa_enabled',
  MFA_DISABLED: 'user.mfa_disabled',

  // Team Management
  TEAM_MEMBER_INVITED: 'team.member_invited',
  TEAM_MEMBER_REMOVED: 'team.member_removed',
  TEAM_ROLE_CHANGED: 'team.role_changed',
  TEAM_INVITATION_ACCEPTED: 'team.invitation_accepted',

  // Data Operations
  SUMMARY_CREATED: 'summary.created',
  SUMMARY_DELETED: 'summary.deleted',
  SUMMARY_EXPORTED: 'summary.exported',
  FILE_UPLOADED: 'file.uploaded',
  FILE_DELETED: 'file.deleted',

  // Integrations
  SLACK_CONNECTED: 'integration.slack_connected',
  SLACK_DISCONNECTED: 'integration.slack_disconnected',
  CRM_CONNECTED: 'integration.crm_connected',
  CRM_DISCONNECTED: 'integration.crm_disconnected',
  CRM_SYNC_PERFORMED: 'integration.crm_sync',

  // Billing & Subscriptions
  SUBSCRIPTION_CREATED: 'billing.subscription_created',
  SUBSCRIPTION_UPDATED: 'billing.subscription_updated',
  SUBSCRIPTION_CANCELLED: 'billing.subscription_cancelled',
  PAYMENT_SUCCEEDED: 'billing.payment_succeeded',
  PAYMENT_FAILED: 'billing.payment_failed',

  // Settings & Configuration
  SETTINGS_UPDATED: 'settings.updated',
  API_KEY_CREATED: 'api.key_created',
  API_KEY_DELETED: 'api.key_deleted',
  WEBHOOK_CONFIGURED: 'webhook.configured',

  // Security Events
  SUSPICIOUS_LOGIN: 'security.suspicious_login',
  RATE_LIMIT_EXCEEDED: 'security.rate_limit_exceeded',
  UNAUTHORIZED_ACCESS: 'security.unauthorized_access',
  DATA_BREACH_DETECTED: 'security.data_breach_detected',
} as const;

/**
 * Log audit event
 */
export async function logAuditEvent(
  organizationId: string,
  userId: string,
  userEmail: string,
  action: string,
  resourceType: string,
  details: Record<string, any>,
  options: {
    resourceId?: string;
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
    riskLevel?: 'low' | 'medium' | 'high' | 'critical';
    status?: 'success' | 'failure' | 'warning';
    metadata?: Record<string, any>;
  } = {}
): Promise<{ success: boolean; logId?: string; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    const auditLog: Omit<AuditLog, 'id'> = {
      organization_id: organizationId,
      user_id: userId,
      user_email: userEmail,
      action,
      resource_type: resourceType,
      resource_id: options.resourceId,
      details,
      ip_address: options.ipAddress,
      user_agent: options.userAgent,
      session_id: options.sessionId,
      risk_level: options.riskLevel || determineRiskLevel(action),
      status: options.status || 'success',
      timestamp: new Date().toISOString(),
      metadata: options.metadata,
    };

    const { data, error } = await supabase
      .from('audit_logs')
      .insert(auditLog)
      .select('id')
      .single();

    if (error) {
      throw error;
    }

    // Check for security incidents
    if (auditLog.risk_level === 'critical' || auditLog.status === 'failure') {
      await handleSecurityIncident(auditLog);
    }

    return { success: true, logId: data.id };

  } catch (error) {
    console.error('Error logging audit event:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to log audit event',
    };
  }
}

/**
 * Get audit logs with filtering
 */
export async function getAuditLogs(
  filters: AuditFilters
): Promise<{ success: boolean; logs?: AuditLog[]; total?: number; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    let query = supabase
      .from('audit_logs')
      .select('*', { count: 'exact' });

    // Apply filters
    if (filters.organization_id) {
      query = query.eq('organization_id', filters.organization_id);
    }
    if (filters.user_id) {
      query = query.eq('user_id', filters.user_id);
    }
    if (filters.action) {
      query = query.eq('action', filters.action);
    }
    if (filters.resource_type) {
      query = query.eq('resource_type', filters.resource_type);
    }
    if (filters.risk_level) {
      query = query.eq('risk_level', filters.risk_level);
    }
    if (filters.status) {
      query = query.eq('status', filters.status);
    }
    if (filters.start_date) {
      query = query.gte('timestamp', filters.start_date);
    }
    if (filters.end_date) {
      query = query.lte('timestamp', filters.end_date);
    }

    // Apply pagination
    if (filters.offset) {
      query = query.range(filters.offset, (filters.offset + (filters.limit || 50)) - 1);
    } else if (filters.limit) {
      query = query.limit(filters.limit);
    }

    // Order by timestamp descending
    query = query.order('timestamp', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    return { 
      success: true, 
      logs: data || [], 
      total: count || 0 
    };

  } catch (error) {
    console.error('Error getting audit logs:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get audit logs',
    };
  }
}

/**
 * Generate compliance report
 */
export async function generateComplianceReport(
  organizationId: string,
  startDate: string,
  endDate: string
): Promise<{ success: boolean; report?: ComplianceReport; error?: string }> {
  try {
    const supabase = await createSupabaseServerClient();

    // Get all audit logs for the period
    const { data: logs, error } = await supabase
      .from('audit_logs')
      .select('*')
      .eq('organization_id', organizationId)
      .gte('timestamp', startDate)
      .lte('timestamp', endDate)
      .order('timestamp', { ascending: false });

    if (error) {
      throw error;
    }

    const auditLogs = logs || [];

    // Calculate metrics
    const eventsByRiskLevel = auditLogs.reduce((acc, log) => {
      acc[log.risk_level] = (acc[log.risk_level] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const eventsByAction = auditLogs.reduce((acc, log) => {
      acc[log.action] = (acc[log.action] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const eventsByUser = (Object.values(
      auditLogs.reduce((acc, log) => {
        if (!acc[log.user_id]) {
          acc[log.user_id] = {
            user_id: log.user_id,
            user_email: log.user_email,
            event_count: 0,
          };
        }
        acc[log.user_id].event_count++;
        return acc;
      }, {} as Record<string, { user_id: string; user_email: string; event_count: number }>)
    ) as { user_id: string; user_email: string; event_count: number }[]).sort((a, b) => b.event_count - a.event_count);

    // Identify security incidents
    const securityIncidents = auditLogs.filter(log => 
      log.risk_level === 'critical' || 
      log.status === 'failure' ||
      log.action.includes('security.')
    );

    // Calculate compliance score
    const complianceScore = calculateComplianceScore(auditLogs);

    // Generate recommendations
    const recommendations = generateRecommendations(auditLogs, securityIncidents);

    const report: ComplianceReport = {
      organization_id: organizationId,
      report_period: { start: startDate, end: endDate },
      total_events: auditLogs.length,
      events_by_risk_level: eventsByRiskLevel,
      events_by_action: eventsByAction,
      events_by_user: eventsByUser,
      security_incidents: securityIncidents,
      compliance_score: complianceScore,
      recommendations,
      generated_at: new Date().toISOString(),
    };

    return { success: true, report };

  } catch (error) {
    console.error('Error generating compliance report:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate compliance report',
    };
  }
}

/**
 * Export audit logs
 */
export async function exportAuditLogs(
  filters: AuditFilters,
  format: 'csv' | 'json'
): Promise<{ success: boolean; data?: string; filename?: string; error?: string }> {
  try {
    const result = await getAuditLogs({ ...filters, limit: 10000 });
    
    if (!result.success || !result.logs) {
      return { success: false, error: result.error };
    }

    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `audit-logs-${timestamp}.${format}`;

    if (format === 'csv') {
      const csv = convertToCSV(result.logs);
      return { success: true, data: csv, filename };
    } else {
      const json = JSON.stringify(result.logs, null, 2);
      return { success: true, data: json, filename };
    }

  } catch (error) {
    console.error('Error exporting audit logs:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to export audit logs',
    };
  }
}

// Helper functions

function determineRiskLevel(action: string): 'low' | 'medium' | 'high' | 'critical' {
  if (action.includes('security.') || action.includes('unauthorized') || action.includes('breach')) {
    return 'critical';
  }
  if (action.includes('login_failed') || action.includes('password') || action.includes('mfa')) {
    return 'high';
  }
  if (action.includes('team.') || action.includes('billing.') || action.includes('settings.')) {
    return 'medium';
  }
  return 'low';
}

async function handleSecurityIncident(auditLog: Omit<AuditLog, 'id'>): Promise<void> {
  // Send security alerts, notifications, etc.
  console.log('Security incident detected:', auditLog.action);
  
  // Could integrate with security monitoring tools
  SentryTracker.captureException(new Error(`Security incident: ${auditLog.action}`), {
    component: 'audit-logging',
    action: 'security-incident',
    extra: auditLog
  });
}

function calculateComplianceScore(logs: AuditLog[]): number {
  if (logs.length === 0) return 100;

  const criticalEvents = logs.filter(log => log.risk_level === 'critical').length;
  const failureEvents = logs.filter(log => log.status === 'failure').length;
  
  const totalRiskEvents = criticalEvents + failureEvents;
  const riskRatio = totalRiskEvents / logs.length;
  
  // Score from 0-100, where lower risk ratio = higher score
  return Math.max(0, Math.round((1 - riskRatio) * 100));
}

function generateRecommendations(logs: AuditLog[], incidents: AuditLog[]): string[] {
  const recommendations: string[] = [];

  if (incidents.length > 0) {
    recommendations.push('Review and investigate security incidents');
  }

  const failedLogins = logs.filter(log => log.action === AUDIT_ACTIONS.USER_LOGIN_FAILED);
  if (failedLogins.length > 10) {
    recommendations.push('Consider implementing additional login security measures');
  }

  const mfaEvents = logs.filter(log => log.action === AUDIT_ACTIONS.MFA_ENABLED);
  if (mfaEvents.length === 0) {
    recommendations.push('Encourage users to enable multi-factor authentication');
  }

  return recommendations;
}

function convertToCSV(logs: AuditLog[]): string {
  if (logs.length === 0) return '';

  const headers = [
    'timestamp', 'user_email', 'action', 'resource_type', 'resource_id',
    'risk_level', 'status', 'ip_address', 'details'
  ];

  const rows = logs.map(log => [
    log.timestamp,
    log.user_email,
    log.action,
    log.resource_type,
    log.resource_id || '',
    log.risk_level,
    log.status,
    log.ip_address || '',
    JSON.stringify(log.details)
  ]);

  return [headers, ...rows]
    .map(row => row.map(field => `"${field}"`).join(','))
    .join('\n');
}
