'use client';

import { useState, useEffect } from 'react';
// No auth mode - removed user context import
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CreditCard, 
  Calendar, 
  DollarSign, 
  Settings, 
  Download,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { toast } from 'sonner';
import AuthGuard from '@/components/AuthGuard';
import { getCurrentUserClient } from '@/lib/user-management-client';

interface Subscription {
  id: string;
  status: string;
  plan: string;
  amount: number;
  currency: string;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  stripeCustomerId: string;
}

function BillingPageContent() {
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [loadingSubscription, setLoadingSubscription] = useState(true);

  // Get current user on mount
  useEffect(() => {
    const loadUser = async () => {
      try {
        const currentUser = await getCurrentUserClient();
        setUser(currentUser);
      } catch (error) {
        console.error('Failed to load user:', error);
      } finally {
        setIsLoading(false);
      }
    };
    loadUser();
  }, []);
  const [loadingAction, setLoadingAction] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (user && mounted) {
      fetchSubscription();
    }
  }, [user, mounted]);

  const fetchSubscription = async () => {
    try {
      const response = await fetch('/api/subscription/status');
      if (response.ok) {
        const data = await response.json();
        setSubscription(data.subscription);
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
      toast.error('Failed to load subscription details');
    } finally {
      setLoadingSubscription(false);
    }
  };

  const handleManageBilling = async () => {
    setLoadingAction(true);
    try {
      const response = await fetch('/api/stripe/portal', {
        method: 'POST',
      });

      if (response.ok) {
        const data = await response.json();
        if (typeof window !== 'undefined') {
          window.location.href = data.url;
        }
      } else {
        throw new Error('Failed to create portal session');
      }
    } catch (error) {
      console.error('Error opening billing portal:', error);
      toast.error('Failed to open billing portal');
    } finally {
      setLoadingAction(false);
    }
  };

  const handleUpgrade = async (plan: string) => {
    setLoadingAction(true);
    try {
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          plan: plan.toUpperCase(),
          // organization_id will be fetched from user profile
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (typeof window !== 'undefined') {
          window.location.href = data.checkout_url;
        }
      } else {
        throw new Error('Failed to create checkout session');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      toast.error('Failed to start upgrade process');
    } finally {
      setLoadingAction(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Active</Badge>;
      case 'trialing':
        return <Badge className="bg-blue-100 text-blue-800"><Clock className="w-3 h-3 mr-1" />Trial</Badge>;
      case 'past_due':
        return <Badge className="bg-yellow-100 text-yellow-800"><AlertCircle className="w-3 h-3 mr-1" />Past Due</Badge>;
      case 'canceled':
        return <Badge className="bg-red-100 text-red-800">Canceled</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (!mounted || isLoading || loadingSubscription) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="h-48 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Billing & Subscription</h1>
        <p className="text-gray-600">Manage your subscription and billing information</p>
      </div>

      {/* Current Subscription */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            Current Subscription
          </CardTitle>
          <CardDescription>
            Your current plan and billing information
          </CardDescription>
        </CardHeader>
        <CardContent>
          {subscription ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">{subscription.plan} Plan</h3>
                  <p className="text-gray-600">
                    {formatAmount(subscription.amount, subscription.currency)}/month
                  </p>
                </div>
                {getStatusBadge(subscription.status)}
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">Current Period</p>
                    <p className="text-sm text-gray-600">
                      {formatDate(subscription.currentPeriodStart)} - {formatDate(subscription.currentPeriodEnd)}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">Next Payment</p>
                    <p className="text-sm text-gray-600">
                      {subscription.cancelAtPeriodEnd 
                        ? 'Subscription will end' 
                        : formatAmount(subscription.amount, subscription.currency)
                      } on {formatDate(subscription.currentPeriodEnd)}
                    </p>
                  </div>
                </div>
              </div>

              {subscription.cancelAtPeriodEnd && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="w-4 h-4 text-yellow-600" />
                    <p className="text-sm text-yellow-800">
                      Your subscription is set to cancel at the end of the current period.
                    </p>
                  </div>
                </div>
              )}

              <div className="flex gap-3">
                <Button 
                  onClick={handleManageBilling}
                  disabled={loadingAction}
                  className="flex items-center gap-2"
                >
                  <Settings className="w-4 h-4" />
                  Manage Billing
                </Button>
                
                <Button 
                  variant="outline"
                  onClick={() => {
                    if (typeof window !== 'undefined') {
                      window.open('/api/billing/invoice', '_blank');
                    }
                  }}
                  className="flex items-center gap-2"
                >
                  <Download className="w-4 h-4" />
                  Download Invoice
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-600 mb-4">You don't have an active subscription</p>
              <div className="space-y-2">
                <Button 
                  onClick={() => handleUpgrade('pro')}
                  disabled={loadingAction}
                  className="mr-2"
                >
                  Upgrade to Pro ($29/month)
                </Button>
                <Button 
                  onClick={() => handleUpgrade('enterprise')}
                  disabled={loadingAction}
                  variant="outline"
                >
                  Upgrade to Enterprise ($99/month)
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Plan Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Available Plans</CardTitle>
          <CardDescription>
            Compare features and upgrade your plan
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Free Plan */}
            <div className="border rounded-lg p-4">
              <h3 className="font-semibold text-lg mb-2">Free</h3>
              <p className="text-2xl font-bold mb-4">$0<span className="text-sm font-normal">/month</span></p>
              <ul className="space-y-2 text-sm">
                <li>• 1 Slack workspace</li>
                <li>• Basic AI summaries</li>
                <li>• Email support</li>
                <li>• Standard exports</li>
              </ul>
            </div>

            {/* Pro Plan */}
            <div className="border rounded-lg p-4 border-blue-200 bg-blue-50">
              <h3 className="font-semibold text-lg mb-2">Pro</h3>
              <p className="text-2xl font-bold mb-4">$29<span className="text-sm font-normal">/month</span></p>
              <ul className="space-y-2 text-sm mb-4">
                <li>• 3 Slack workspaces</li>
                <li>• Advanced AI summaries</li>
                <li>• Priority support</li>
                <li>• All export formats</li>
                <li>• Smart tagging</li>
              </ul>
              {(!subscription || subscription.plan !== 'PRO') && (
                <Button 
                  onClick={() => handleUpgrade('pro')}
                  disabled={loadingAction}
                  className="w-full"
                >
                  Upgrade to Pro
                </Button>
              )}
            </div>

            {/* Enterprise Plan */}
            <div className="border rounded-lg p-4">
              <h3 className="font-semibold text-lg mb-2">Enterprise</h3>
              <p className="text-2xl font-bold mb-4">$99<span className="text-sm font-normal">/month</span></p>
              <ul className="space-y-2 text-sm mb-4">
                <li>• Unlimited workspaces</li>
                <li>• Premium AI models</li>
                <li>• 24/7 support</li>
                <li>• Advanced analytics</li>
                <li>• CRM integrations</li>
                <li>• Custom features</li>
              </ul>
              {(!subscription || subscription.plan !== 'ENTERPRISE') && (
                <Button 
                  onClick={() => handleUpgrade('enterprise')}
                  disabled={loadingAction}
                  variant="outline"
                  className="w-full"
                >
                  Upgrade to Enterprise
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function BillingPage() {
  return (
    <AuthGuard>
      <BillingPageContent />
    </AuthGuard>
  );
}
