import { NextRequest, NextResponse } from 'next/server';
import { routeAIRequest } from '@/lib/ai-routing-service';
import { getCurrentUser } from '@/lib/user-management';

export async function POST(request: NextRequest) {
  try {
    // Get user (public mode - no authentication required)
    const user = await getCurrentUser();

    if (!user) {
      // Create anonymous user for public mode
      const anonymousUser = {
        id: 'anonymous-' + Date.now(),
        email: '<EMAIL>',
        name: 'Anonymous User'
      };
      console.log('📝 Using anonymous user for summarization:', anonymousUser.id);
    }

    const userId = user?.id || 'anonymous-' + Date.now();
    console.log(`📝 Summarize: Processing request for user ${userId}`);

    const body = await request.json();
    const { transcriptText, teamId, context, preferredModel, organizationId } = body;

    // Validate required fields
    if (!transcriptText) {
      return NextResponse.json(
        { error: 'Missing required field: transcriptText' },
        { status: 400 }
      );
    }

    // Extract user info for tracking
    const authenticatedUserId = user.id;
    const userTeamId = teamId || user.orgId;
    const userOrganizationId = organizationId || user.orgId;
    const userPlan = user.plan?.toUpperCase() || 'FREE';

    console.log(`📊 User context: ${authenticatedUserId} | Team: ${userTeamId} | Plan: ${userPlan}`);

    // Generate summary using AI
    const startTime = Date.now();

    try {
      // Use the AI routing service for intelligent model selection and usage tracking
      const aiRoutingResult = await routeAIRequest({
        userId: authenticatedUserId,
        text: transcriptText,
        preferredModel,
        context: context || 'meeting_summary',
        organizationId: userOrganizationId,
      });

      if (!aiRoutingResult.success) {
        throw new Error(aiRoutingResult.error || 'AI routing failed');
      }

      const summaryResponse = aiRoutingResult.response!;
      const processingTime = Date.now() - startTime;

      console.log(`✅ Summary generated: ${summaryResponse.text.length} chars in ${processingTime}ms using ${summaryResponse.model}`);

      // DEV MODE: No database storage required
      const summaryId = 'dev-summary-' + Date.now();
      console.log('📄 Dev mode: Skipping database storage for summary');

      // Return successful response with mock data
      return NextResponse.json({
        success: true,
        summary: {
          id: summaryId,
          user_id: authenticatedUserId,
          team_id: userTeamId,
          title: `Summary - ${new Date().toLocaleDateString()}`,
          summary_text: summaryResponse.text,
          summary: {
            text: summaryResponse.text,
            skills: [],
            redFlags: [],
            actions: [],
            sentiment: 'neutral',
            keyPoints: [],
            participants: [],
            duration: null,
            meetingType: context || 'general'
          },
          source_type: 'manual',
          ai_model: summaryResponse.model,
          ai_cost: summaryResponse.cost,
          ai_tokens: summaryResponse.tokens,
          processing_time: processingTime,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          organization_id: userOrganizationId,
        },
        summaryId,
        model: summaryResponse.model,
        cost: summaryResponse.cost,
        tokens: summaryResponse.tokens,
        processingTime,
        plan: userPlan,
        features: {
          smartTagging: userPlan !== 'FREE',
          autoPosting: userPlan !== 'FREE',
          crmIntegration: userPlan !== 'FREE'
        },
        message: 'Summary created successfully in dev mode.'
      });

    } catch (aiError) {
      const processingTime = Date.now() - startTime;

      console.error('AI generation error:', aiError);

      return NextResponse.json(
        { error: 'Failed to generate summary', details: aiError instanceof Error ? aiError.message : 'Unknown error' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Summarize API error:', error);

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
