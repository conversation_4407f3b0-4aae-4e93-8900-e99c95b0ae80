'use client';

// Disable static generation for this page
export const dynamic = 'force-dynamic';

/**
 * Enhanced Upload Page - Complete SaaS Features
 * 
 * Features:
 * ✅ File upload with drag & drop
 * ✅ AI-powered summarization
 * ✅ Real-time progress tracking
 * ✅ Export system integration
 * ✅ Notification system
 * ✅ Analytics tracking
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Upload, 
  FileText, 
  Brain, 
  Zap, 
  Download,
  MessageSquare,
  Settings,
  BarChart3,
  Sparkles
} from 'lucide-react';
import { toast } from 'sonner';
import FileUploadSystem from '@/components/FileUploadSystem';
import ExportSystem from '@/components/ExportSystem';
import NotificationSystem from '@/components/NotificationSystem';
import AuthGuard from '@/components/AuthGuard';
import { analytics } from '@/lib/posthog.client';

interface ProcessedSummary {
  id: string;
  title: string;
  content: string;
  keyPoints: string[];
  actionItems: string[];
  redFlags: string[];
  skills: string[];
  tags: string[];
  createdAt: Date;
  fileId?: string;
  fileName?: string;
}

export default function EnhancedUploadPage() {
  const [summaries, setSummaries] = useState<ProcessedSummary[]>([]);
  const [selectedSummary, setSelectedSummary] = useState<ProcessedSummary | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeTab, setActiveTab] = useState('upload');

  useEffect(() => {
    // Track page view
    analytics.track('upload_page_viewed');

    // Load existing summaries from API
    loadUserSummaries();
  }, []);

  const loadUserSummaries = async () => {
    try {
      const response = await fetch('/api/summaries');
      if (response.ok) {
        const data = await response.json();
        const userSummaries: ProcessedSummary[] = data.data?.map((summary: any) => ({
          id: summary.id,
          title: summary.title,
          content: summary.content,
          keyPoints: summary.summary_data?.key_points || [],
          actionItems: summary.summary_data?.action_items || [],
          redFlags: summary.summary_data?.red_flags || [],
          skills: summary.summary_data?.skills || [],
          tags: summary.tags || [],
          createdAt: new Date(summary.created_at),
          fileName: summary.metadata?.file_name
        })) || [];

        setSummaries(userSummaries);
        if (userSummaries.length > 0) {
          setSelectedSummary(userSummaries[0]);
        }
      }
    } catch (error) {
      console.error('Failed to load summaries:', error);
    }
  };

  const handleFileProcessed = (file: any) => {
    console.log('File processed:', file);
    setIsProcessing(false);
    
    // Track file processing
    analytics.track('file_processed_success', {
      file_name: file.name,
      file_type: file.type,
      file_size: file.size
    });

    toast.success(`File "${file.name}" processed successfully!`);
  };

  const handleSummaryGenerated = (summary: any) => {
    console.log('Summary generated:', summary);
    
    const newSummary: ProcessedSummary = {
      id: summary.id || `summary_${Date.now()}`,
      title: summary.title || 'New Summary',
      content: summary.summary || summary.content || '',
      keyPoints: summary.keyPoints || [],
      actionItems: summary.actionItems || [],
      redFlags: summary.redFlags || [],
      skills: summary.skills || [],
      tags: summary.tags || [],
      createdAt: new Date(),
      fileName: summary.fileName
    };

    setSummaries(prev => [newSummary, ...prev]);
    setSelectedSummary(newSummary);
    setActiveTab('summaries');

    // Track summary generation
    analytics.track('summary_generated_success', {
      summary_id: newSummary.id,
      content_length: newSummary.content.length,
      key_points_count: newSummary.keyPoints.length,
      action_items_count: newSummary.actionItems.length
    });

    toast.success('AI summary generated successfully!');
  };

  const handleExportComplete = (exportJob: any) => {
    console.log('Export completed:', exportJob);
    
    analytics.track('export_completed_success', {
      export_id: exportJob.id,
      summary_id: selectedSummary?.id,
      format: exportJob.format
    });

    toast.success(`Export completed successfully!`);
  };

  const SummaryCard = ({ summary }: { summary: ProcessedSummary }) => (
    <Card 
      className={`cursor-pointer transition-all hover:shadow-md ${
        selectedSummary?.id === summary.id ? 'ring-2 ring-blue-500' : ''
      }`}
      onClick={() => setSelectedSummary(summary)}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{summary.title}</CardTitle>
          <Badge variant="secondary">
            {summary.createdAt.toLocaleDateString()}
          </Badge>
        </div>
        {summary.fileName && (
          <CardDescription className="flex items-center gap-1">
            <FileText className="h-4 w-4" />
            {summary.fileName}
          </CardDescription>
        )}
      </CardHeader>
      <CardContent>
        <p className="text-sm text-gray-600 line-clamp-2 mb-3">
          {summary.content}
        </p>
        <div className="flex flex-wrap gap-1">
          {summary.tags.slice(0, 3).map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {summary.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{summary.tags.length - 3} more
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );

  const SummaryDetails = ({ summary }: { summary: ProcessedSummary }) => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">{summary.title}</h2>
        <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
          <span>Created: {summary.createdAt.toLocaleString()}</span>
          {summary.fileName && (
            <span className="flex items-center gap-1">
              <FileText className="h-4 w-4" />
              {summary.fileName}
            </span>
          )}
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-700 leading-relaxed">{summary.content}</p>
        </CardContent>
      </Card>

      {summary.keyPoints.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Key Points
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {summary.keyPoints.map((point, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                  <span>{point}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {summary.actionItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Action Items
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {summary.actionItems.map((item, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                  <span>{item}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {summary.redFlags.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <MessageSquare className="h-5 w-5" />
              Red Flags
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {summary.redFlags.map((flag, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></span>
                  <span className="text-red-700">{flag}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {summary.skills.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              Skills & Technologies
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {summary.skills.map((skill) => (
                <Badge key={skill} variant="secondary">
                  {skill}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  return (
      <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold">AI-Powered Summarization</h1>
          <p className="text-gray-600">
            Upload files, generate summaries, and export insights
          </p>
        </div>
        <div className="flex items-center gap-2">
          <NotificationSystem />
          <Button variant="outline" onClick={() => setActiveTab('analytics')}>
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="upload">Upload & Process</TabsTrigger>
          <TabsTrigger value="summaries">Summaries ({summaries.length})</TabsTrigger>
          <TabsTrigger value="export">Export & Share</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-6">
          <FileUploadSystem
            onFileProcessed={handleFileProcessed}
            onSummaryGenerated={handleSummaryGenerated}
            maxFileSize={20}
            acceptedTypes={['.pdf', '.docx', '.txt', '.md']}
          />
        </TabsContent>

        <TabsContent value="summaries" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Summary List */}
            <div className="lg:col-span-1 space-y-4">
              <h3 className="text-lg font-semibold">Recent Summaries</h3>
              {summaries.length === 0 ? (
                <Card>
                  <CardContent className="p-6 text-center">
                    <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <p className="text-gray-600">No summaries yet</p>
                    <p className="text-sm text-gray-500">Upload a file to get started</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-3">
                  {summaries.map((summary) => (
                    <SummaryCard key={summary.id} summary={summary} />
                  ))}
                </div>
              )}
            </div>

            {/* Summary Details */}
            <div className="lg:col-span-2">
              {selectedSummary ? (
                <SummaryDetails summary={selectedSummary} />
              ) : (
                <Card>
                  <CardContent className="p-12 text-center">
                    <Brain className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-lg font-semibold mb-2">Select a Summary</h3>
                    <p className="text-gray-600">
                      Choose a summary from the list to view details
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="export" className="space-y-6">
          {selectedSummary ? (
            <ExportSystem
              summaryId={selectedSummary.id}
              summaryTitle={selectedSummary.title}
              onExportComplete={handleExportComplete}
            />
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <Download className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-semibold mb-2">No Summary Selected</h3>
                <p className="text-gray-600 mb-4">
                  Select a summary to export it in various formats
                </p>
                <Button onClick={() => setActiveTab('summaries')}>
                  View Summaries
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardContent className="p-12 text-center">
              <BarChart3 className="h-16 w-16 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-semibold mb-2">Analytics Dashboard</h3>
              <p className="text-gray-600 mb-4">
                View detailed analytics and insights about your usage
              </p>
              <Button onClick={() => window.open('/analytics', '_blank')}>
                Open Analytics Dashboard
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
  );
}
