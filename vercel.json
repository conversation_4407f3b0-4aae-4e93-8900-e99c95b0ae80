{"version": 2, "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "framework": "nextjs", "regions": ["iad1", "fra1", "hnd1"], "functions": {"app/api/**/*.ts": {"maxDuration": 30}, "app/api/webhooks/**/*.ts": {"maxDuration": 60}, "app/api/export/**/*.ts": {"maxDuration": 120}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}]}], "redirects": [{"source": "/upload", "destination": "/upload-enhanced", "permanent": false}, {"source": "/login", "destination": "/auth/login", "permanent": false}, {"source": "/signup", "destination": "/auth/signup", "permanent": false}, {"source": "/admin", "destination": "/admin/dashboard", "permanent": false}, {"source": "/docs", "destination": "/docs/getting-started", "permanent": false}], "rewrites": [{"source": "/api/webhooks/:path*", "destination": "/api/webhooks/:path*"}, {"source": "/health", "destination": "/api/health"}, {"source": "/metrics", "destination": "/api/metrics"}], "crons": [{"path": "/api/cron/cleanup", "schedule": "0 2 * * *"}, {"path": "/api/cron/analytics", "schedule": "0 1 * * *"}, {"path": "/api/cron/retention", "schedule": "0 9 * * *"}, {"path": "/api/cron/exports", "schedule": "*/15 * * * *"}], "env": {"NEXT_PUBLIC_VERCEL_ENV": "@vercel-env", "NEXT_PUBLIC_VERCEL_URL": "@vercel-url"}}