/**
 * Production User Management System
 *
 * Handles user management for public access mode
 * Creates anonymous users for tracking while maintaining public access
 */

import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

export interface User {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
  last_active_at?: string;
  settings?: UserSettings;
  // Dev mode properties
  orgId?: string;
  plan?: string;
}

export interface UserSettings {
  theme: 'light' | 'dark' | 'system';
  notifications: {
    email: boolean;
    slack: boolean;
    push: boolean;
  };
  timezone: string;
  language: string;
}

export interface Organization {
  id: string;
  name: string;
  slug: string;
  plan: 'FREE' | 'PRO' | 'ENTERPRISE';
  created_at: string;
  updated_at: string;
  settings: OrganizationSettings;
}

export interface OrganizationSettings {
  features: string[];
  limits: {
    summaries_per_month: number;
    file_size_mb: number;
    exports_per_month: number;
    users: number;
  };
  integrations: {
    slack_enabled: boolean;
    email_enabled: boolean;
  };
}

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

/**
 * Get or create anonymous user for public access
 */
export async function getCurrentUser(): Promise<User | null> {
  try {
    // For public access, create a session-based anonymous user
    const sessionId = typeof window !== 'undefined'
      ? localStorage.getItem('anonymous_session_id') || generateAnonymousSession()
      : generateAnonymousSession();

    // Create anonymous user object
    const anonymousUser: User = {
      id: `anon_${sessionId}`,
      email: `anonymous_${sessionId}@public.local`,
      name: 'Anonymous User',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      orgId: `org_${sessionId}`,
      plan: 'FREE'
    };

    return anonymousUser;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Get current user from session (client-side)
 */
export async function getCurrentUserClient(): Promise<User | null> {
  return getCurrentUser();
}

/**
 * Generate anonymous session ID
 */
function generateAnonymousSession(): string {
  const sessionId = uuidv4().substring(0, 8);
  if (typeof window !== 'undefined') {
    localStorage.setItem('anonymous_session_id', sessionId);
  }
  return sessionId;
}

/**
 * Get user's organization - DEV ONLY
 */
export async function getUserOrganization(userId: string): Promise<Organization | null> {
  // Return hardcoded dev organization
  return {
    id: 'org-dev-001',
    name: 'Dev Organization',
    slug: 'dev-org',
    settings: {
      features: ['ai_summaries', 'slack_integration', 'exports', 'analytics'],
      limits: {
        monthly_summaries: 100,
        team_members: 10,
        storage_gb: 5
      }
    },
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  };
}

/**
 * Create or update user profile - DEV ONLY
 */
export async function upsertUserProfile(user: Partial<User>): Promise<User | null> {
  // Return the dev user (no actual database operation)
  return DEFAULT_DEV_USER;
}

/**
 * Update user settings - DEV ONLY
 */
export async function updateUserSettings(userId: string, settings: Partial<UserSettings>): Promise<boolean> {
  // Always return success (no actual database operation)
  return true;
}

/**
 * Get default user settings
 */
export function getDefaultUserSettings(): UserSettings {
  return {
    theme: 'system',
    notifications: {
      email: true,
      slack: false,
      push: false
    },
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    language: 'en'
  };
}

/**
 * Check if user has access to feature - DEV ONLY
 */
export async function hasFeatureAccess(userId: string, feature: string): Promise<boolean> {
  // All features are available in dev mode
  return true;
}

/**
 * Get user's usage limits - DEV ONLY
 */
export async function getUserLimits(userId: string): Promise<OrganizationSettings['limits'] | null> {
  // Return dev limits
  return {
    monthly_summaries: 100,
    team_members: 10,
    storage_gb: 5
  };
}

/**
 * Track user activity - DEV ONLY
 */
export async function trackUserActivity(userId: string): Promise<void> {
  // No-op in dev mode
  console.log('Dev mode: User activity tracked for', userId);
}
