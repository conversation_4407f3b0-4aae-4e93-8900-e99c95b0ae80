/**
 * Subscription Management Service
 * Public Demo Mode - No Stripe integration required
 */

import { createSupabaseServerClient } from './supabase-server';

// Subscription Tiers for demo mode
export type SubscriptionTier = 'FREE' | 'PRO' | 'ENTERPRISE';

// Demo mode pricing plans (no Stripe required)
const DEMO_PRICING_PLANS = {
  FREE: {
    id: 'FREE',
    name: 'Free',
    price: 0,
    currency: 'usd',
    interval: 'month',
    features: [
      '10 summaries per month',
      'DeepSeek R1 AI model',
      'Basic Slack integration',
      'Email support',
      'Standard templates'
    ],
    limits: {
      monthlySummaries: 10,
      teamMembers: 1,
      aiModels: ['deepseek-r1'],
      crmIntegrations: 0,
      scheduledPosts: 0,
      analytics: false,
      auditLogs: false,
      ssoIntegration: false
    },
  },
  PRO: {
    id: 'PRO',
    name: 'Pro',
    price: 29,
    currency: 'usd',
    interval: 'month',
    features: [
      '100 summaries per month',
      'All AI models (GPT-4o-mini, Claude)',
      'Advanced Slack integration',
      'Priority support',
      'Custom templates',
      'Basic analytics'
    ],
    limits: {
      monthlySummaries: 100,
      teamMembers: 5,
      aiModels: ['deepseek-r1', 'gpt-4o-mini', 'claude-3-haiku'],
      crmIntegrations: 2,
      scheduledPosts: 10,
      analytics: true,
      auditLogs: false,
      ssoIntegration: false
    },
  },
  ENTERPRISE: {
    id: 'ENTERPRISE',
    name: 'Enterprise',
    price: 99,
    currency: 'usd',
    interval: 'month',
    features: [
      'Unlimited summaries',
      'All AI models + premium',
      'Full Slack automation',
      '24/7 support',
      'Custom integrations',
      'Advanced analytics',
      'Audit logs',
      'SSO integration'
    ],
    limits: {
      monthlySummaries: -1, // Unlimited
      teamMembers: -1, // Unlimited
      aiModels: ['deepseek-r1', 'gpt-4o-mini', 'claude-3-haiku', 'gpt-4o', 'claude-3-opus'],
      crmIntegrations: -1, // Unlimited
      scheduledPosts: -1, // Unlimited
      analytics: true,
      auditLogs: true,
      ssoIntegration: true
    },
  }
};

export interface UserSubscription {
  id: string;
  user_id: string;
  subscription_tier: SubscriptionTier;
  status: string;
  monthly_summary_limit: number;
  monthly_summaries_used: number;
  cancel_at_period_end: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Get user's current subscription
 */
export async function getUserSubscription(userId: string): Promise<UserSubscription | null> {
  try {
    if (!userId) {
      throw new Error('User ID is required');
    }

    const supabase = await createSupabaseServerClient();

    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No subscription found, create default FREE subscription
        return await createDefaultSubscription(userId);
      }
      console.error('Error fetching subscription:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getUserSubscription:', error);
    return null;
  }
}

/**
 * Create default FREE subscription for new users
 */
export async function createDefaultSubscription(userId: string): Promise<UserSubscription> {
  try {
    // For all users, fetch from database
    const supabase = await createSupabaseServerClient();

    const freePlan = DEMO_PRICING_PLANS.FREE;

    const { data, error } = await supabase
      .from('subscriptions')
      .insert({
        user_id: userId,
        subscription_tier: 'FREE',
        status: 'active',
        monthly_summary_limit: freePlan.limits.monthlySummaries,
        monthly_summaries_used: 0,
        cancel_at_period_end: false
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating default subscription:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in createDefaultSubscription:', error);
    throw error;
  }
}

/**
 * Demo mode checkout session (no actual payment processing)
 */
export async function createCheckoutSession(
  userId: string,
  tier: 'PRO' | 'ENTERPRISE',
  userEmail: string
): Promise<{ url: string; sessionId: string }> {
  try {
    // In demo mode, simulate successful checkout
    console.log(`🎭 Demo mode: Simulating checkout for ${tier} plan for user ${userId}`);

    return {
      url: `${process.env.NEXT_PUBLIC_SITE_URL}/dashboard?payment=demo_success&tier=${tier}`,
      sessionId: `demo_session_${Date.now()}`,
    };

  } catch (error) {
    console.error('Error creating demo checkout session:', error);
    throw error;
  }
}

/**
 * Increment usage counter for user
 */
export async function incrementUsage(userId: string, usageType: 'summaries' | 'exports' = 'summaries'): Promise<boolean> {
  try {
    // For all users, check actual usage

    const supabase = await createSupabaseServerClient();

    if (usageType === 'summaries') {
      const { error } = await supabase
        .from('subscriptions')
        .update({
          monthly_summaries_used: supabase.rpc('increment_summaries_used', { user_id: userId })
        })
        .eq('user_id', userId);

      if (error) {
        console.error('Error incrementing usage:', error);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Error in incrementUsage:', error);
    return false;
  }
}
