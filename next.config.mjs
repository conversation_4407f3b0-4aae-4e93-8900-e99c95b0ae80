/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,

  // Production optimizations
  poweredByHeader: false,

  experimental: {
    serverActions: {
      allowedOrigins: ['localhost:3000', '*.vercel.app']
    },
    optimizePackageImports: [
      '@radix-ui/react-icons',
      'lucide-react',
      '@headlessui/react',
      'framer-motion',
      'next-themes'
    ],
    webpackBuildWorker: true,
  },
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60 * 60 * 24 * 30,
    remotePatterns: [
      { protocol: 'https', hostname: '**.slack-edge.com' },
      { protocol: 'https', hostname: '**.googleusercontent.com' },
      { protocol: 'https', hostname: 'secure.gravatar.com' },
      { protocol: 'https', hostname: 'avatars.githubusercontent.com' }
    ],
  },
  typescript: { ignoreBuildErrors: true },
  eslint: { ignoreDuringBuilds: true },

  // Enhanced webpack configuration for chunk loading stability
  webpack: (config, { dev, isServer }) => {
    // Optimize chunk splitting for better loading
    if (!isServer) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          maxSize: 150000, // 150kB max chunks
          cacheGroups: {
            // Separate chunk for React
            react: {
              test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
              name: 'react',
              priority: 30,
              chunks: 'all',
              enforce: true,
            },
            // Separate chunk for UI components
            ui: {
              test: /[\\/]node_modules[\\/](@radix-ui|@headlessui)[\\/]/,
              name: 'ui',
              priority: 20,
              chunks: 'all',
              maxSize: 120000,
            },
            // Separate chunk for icons to prevent loading issues
            icons: {
              test: /[\\/]node_modules[\\/](lucide-react|react-icons)[\\/]/,
              name: 'icons',
              priority: 15,
              chunks: 'all',
              maxSize: 100000,
            },
            // Separate chunk for dropzone
            dropzone: {
              test: /[\\/]node_modules[\\/]react-dropzone[\\/]/,
              name: 'dropzone',
              priority: 14,
              chunks: 'all',
              maxSize: 60000,
            },
          },
        },
      };

      // Enhanced output configuration for chunk loading
      config.output = {
        ...config.output,
        crossOriginLoading: 'anonymous',
        chunkLoadTimeout: 60000, // 60 seconds
        publicPath: dev ? '/_next/' : '/_next/',
        hashFunction: 'xxhash64',
        hashDigestLength: 16,
        chunkFilename: dev
          ? 'static/chunks/[name].js'
          : 'static/chunks/[name].[contenthash].js',
        globalObject: 'typeof self !== \'undefined\' ? self : this',
      };
    }

    return config;
  },

  // Enhanced security headers with proper CSP and MIME types
  async headers() {
    const isDev = process.env.NODE_ENV === 'development';

    return [
      // CSS and Font MIME type headers
      {
        source: '/_next/static/css/(.*)',
        headers: [
          {
            key: 'Content-Type',
            value: 'text/css; charset=utf-8',
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      // Main security headers
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              isDev
                ? "script-src 'self' 'unsafe-inline' 'unsafe-eval' *.vercel.app *.posthog.com *.sentry.io *.openrouter.ai"
                : "script-src 'self' 'unsafe-inline' *.vercel.app *.posthog.com *.sentry.io *.openrouter.ai",
              "style-src 'self' 'unsafe-inline' fonts.googleapis.com *.vercel.app",
              "font-src 'self' fonts.gstatic.com data:",
              "img-src 'self' data: blob: *.dicebear.com *.githubusercontent.com *.gravatar.com *.slack-edge.com *.googleusercontent.com",
              "connect-src 'self' *.posthog.com *.openrouter.ai *.sentry.io *.supabase.co vitals.vercel-insights.com localhost:* 127.0.0.1:* ws: wss:",
              "frame-src 'self' *.vercel.app",
              "media-src 'self' blob: data:",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'none'",
              "upgrade-insecure-requests"
            ].join('; '),
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
