'use client';

/**
 * AuthGuard Component - Public Access Mode
 * 
 * This component allows public access to all routes without authentication.
 * Perfect for demo mode and public SaaS applications.
 */

import React from 'react';

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * AuthGuard that allows public access to all routes
 * No authentication required - perfect for public demo mode
 */
export default function AuthGuard({ children, fallback }: AuthGuardProps) {
  // Always allow access in public mode
  return <>{children}</>;
}

/**
 * Alternative export for compatibility
 */
export { AuthGuard };
